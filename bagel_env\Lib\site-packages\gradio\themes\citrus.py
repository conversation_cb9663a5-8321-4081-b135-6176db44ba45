from __future__ import annotations

from collections.abc import Iterable

from gradio.themes.base import Base
from gradio.themes.utils import colors, fonts, sizes


class Citrus(Base):
    def __init__(
        self,
        *,
        primary_hue: colors.Color | str = colors.amber,
        secondary_hue: colors.Color | str = colors.amber,
        neutral_hue: colors.Color | str = colors.stone,
        spacing_size: sizes.Size | str = sizes.spacing_lg,
        radius_size: sizes.Size | str = sizes.radius_md,
        text_size: sizes.Size | str = sizes.text_md,
        font: fonts.Font | str | Iterable[fonts.Font | str] = (
            fonts.GoogleFont("Ubuntu"),
            "ui-sans-serif",
            "system-ui",
            "sans-serif",
        ),
        font_mono: fonts.Font | str | Iterable[fonts.Font | str] = (
            fonts.GoogleFont("Roboto Mono"),
            "ui-monospace",
            "Consolas",
            "monospace",
        ),
    ):
        super().__init__(
            primary_hue=primary_hue,
            secondary_hue=secondary_hue,
            neutral_hue=neutral_hue,
            spacing_size=spacing_size,
            radius_size=radius_size,
            text_size=text_size,
            font=font,
            font_mono=font_mono,
        )
        self.name = "citrus"
        super().set(
            slider_color="*primary_400",
            background_fill_primary="*neutral_50",
            button_primary_background_fill="*primary_500",
            button_primary_text_color="*button_secondary_text_color",
            button_secondary_background_fill="*primary_400",
            button_primary_background_fill_hover="*button_primary_background_fill",
            button_secondary_background_fill_hover="*button_secondary_background_fill",
            button_primary_shadow_hover="0px 5px 0px 0px *primary_400;",
            button_primary_shadow="0px 3px 0px 0px *primary_400;",
            button_primary_shadow_active="0px 2px 0px 0px *primary_400;",
            button_secondary_shadow_hover="0px 5px 0px 0px *primary_300;",
            button_secondary_shadow="0px 3px 0px 0px *primary_300;",
            button_secondary_shadow_active="0px 2px 0px 0px *primary_300;",
            input_shadow="0px -1px 0px 0px *neutral_300;",
            input_shadow_focus="0px -1px 0px 0px *primary_300;",
            input_background_fill="*neutral_50",
            input_background_fill_focus="*primary_50",
            block_shadow="0px 3px 0px 0px *neutral_300;",
            checkbox_label_background_fill="*neutral_200",
            checkbox_label_background_fill_hover="*checkbox_label_background_fill",
            checkbox_label_background_fill_selected="*primary_400",
            checkbox_label_border_color_selected="*primary_300",
            checkbox_label_border_width="2px",
            slider_color_dark="*primary_500",
            button_primary_background_fill_dark="*primary_600",
            button_secondary_text_color_dark="*neutral_900",
            button_primary_text_color_dark="*button_secondary_text_color",
            button_secondary_background_fill_dark="*primary_500",
            button_primary_background_fill_hover_dark="*button_primary_background_fill",
            button_secondary_background_fill_hover_dark="*button_secondary_background_fill",
            button_primary_shadow_hover_dark="0px 5px 0px 0px *primary_700;",
            button_primary_shadow_dark="0px 3px 0px 0px *primary_700;",
            button_primary_shadow_active_dark="0px 2px 0px 0px *primary_700;",
            button_secondary_shadow_hover_dark="0px 5px 0px 0px *primary_600;",
            button_secondary_shadow_dark="0px 3px 0px 0px *primary_600;",
            button_secondary_shadow_active_dark="0px 2px 0px 0px *primary_600;",
            input_shadow_dark="0px -1px 0px 0px *neutral_700;",
            input_shadow_focus_dark="0px -1px 0px 0px *primary_600;",
            input_background_fill_dark="*neutral_900",
            input_background_fill_focus_dark="none",
            block_shadow_dark="0px 3px 0px 0px *neutral_700;",
            checkbox_label_background_fill_dark="*neutral_700",
            checkbox_label_background_fill_hover_dark="*checkbox_label_background_fill",
            checkbox_label_background_fill_selected_dark="*primary_500",
            checkbox_label_border_color_selected_dark="*primary_600",
            checkbox_label_text_color_selected_dark="*button_primary_text_color",
            checkbox_background_color_selected_dark="*primary_600",
            checkbox_background_color_dark="*neutral_400",
            checkbox_label_border_width_dark="2px",
            button_transform_hover="translateY(-2px);",
            button_transform_active="translateY(1px);",
            button_transition="all 0.1s;",
            button_border_width="0px",
            panel_border_width="1px",
            block_border_width="1px",
            block_border_color="*neutral_300",
            block_background_fill="*neutral_100",
            input_border_width="1px",
            block_label_shadow="none",
            checkbox_shadow="none",
        )
