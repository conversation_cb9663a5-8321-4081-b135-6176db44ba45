{"version": 3, "file": "Example25-rMp0KmBH.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example25.js"], "sourcesContent": ["import { create_ssr_component, validate_component } from \"svelte/internal\";\nimport { V as Video } from \"./Video.js\";\nimport \"./client.js\";\nconst css = {\n  code: \".container.svelte-1de9zxs{flex:none;max-width:none}.container.svelte-1de9zxs video{width:var(--size-full);height:var(--size-full);object-fit:cover}.container.svelte-1de9zxs:hover,.container.selected.svelte-1de9zxs{border-color:var(--border-color-accent)}.container.table.svelte-1de9zxs{margin:0 auto;border:2px solid var(--border-color-primary);border-radius:var(--radius-lg);overflow:hidden;width:var(--size-20);height:var(--size-20);object-fit:cover}.container.gallery.svelte-1de9zxs{height:var(--size-20);max-height:var(--size-20);object-fit:cover}\",\n  map: '{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import Video from \\\\\"./shared/Video.svelte\\\\\";\\\\nimport { playable } from \\\\\"./shared/utils\\\\\";\\\\nimport {} from \\\\\"@gradio/client\\\\\";\\\\nexport let type;\\\\nexport let selected = false;\\\\nexport let value;\\\\nexport let loop;\\\\nlet video;\\\\nasync function init() {\\\\n    video.muted = true;\\\\n    video.playsInline = true;\\\\n    video.controls = false;\\\\n    video.setAttribute(\\\\\"muted\\\\\", \\\\\"\\\\\");\\\\n    await video.play();\\\\n    video.pause();\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if value}\\\\n\\\\t{#if playable()}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tclass=\\\\\"container\\\\\"\\\\n\\\\t\\\\t\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\t\\\\t\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\t\\\\t\\\\tclass:selected\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<Video\\\\n\\\\t\\\\t\\\\t\\\\tmuted\\\\n\\\\t\\\\t\\\\t\\\\tplaysinline\\\\n\\\\t\\\\t\\\\t\\\\tbind:node={video}\\\\n\\\\t\\\\t\\\\t\\\\ton:loadeddata={init}\\\\n\\\\t\\\\t\\\\t\\\\ton:mouseover={video.play.bind(video)}\\\\n\\\\t\\\\t\\\\t\\\\ton:mouseout={video.pause.bind(video)}\\\\n\\\\t\\\\t\\\\t\\\\tsrc={value?.video.url}\\\\n\\\\t\\\\t\\\\t\\\\tis_stream={false}\\\\n\\\\t\\\\t\\\\t\\\\t{loop}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<div>{value}</div>\\\\n\\\\t{/if}\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\t.container {\\\\n\\\\t\\\\tflex: none;\\\\n\\\\t\\\\tmax-width: none;\\\\n\\\\t}\\\\n\\\\t.container :global(video) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.container:hover,\\\\n\\\\t.container.selected {\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\t.container.table {\\\\n\\\\t\\\\tmargin: 0 auto;\\\\n\\\\t\\\\tborder: 2px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\theight: var(--size-20);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}\\\\n\\\\n\\\\t.container.gallery {\\\\n\\\\t\\\\theight: var(--size-20);\\\\n\\\\t\\\\tmax-height: var(--size-20);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA4CC,yBAAW,CACV,IAAI,CAAE,IAAI,CACV,SAAS,CAAE,IACZ,CACA,yBAAU,CAAS,KAAO,CACzB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,KACb,CAEA,yBAAU,MAAM,CAChB,UAAU,wBAAU,CACnB,YAAY,CAAE,IAAI,qBAAqB,CACxC,CACA,UAAU,qBAAO,CAChB,MAAM,CAAE,CAAC,CAAC,IAAI,CACd,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,QAAQ,CAAE,MAAM,CAChB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,KACb,CAEA,UAAU,uBAAS,CAClB,MAAM,CAAE,IAAI,SAAS,CAAC,CACtB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,UAAU,CAAE,KACb\"}'\n};\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  let { value } = $$props;\n  let { loop } = $$props;\n  let video;\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    $$rendered = `${value ? `${`<div class=\"${[\n      \"container svelte-1de9zxs\",\n      (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\")\n    ].join(\" \").trim()}\">${validate_component(Video, \"Video\").$$render(\n      $$result,\n      {\n        muted: true,\n        playsinline: true,\n        src: value?.video.url,\n        is_stream: false,\n        loop,\n        node: video\n      },\n      {\n        node: ($$value) => {\n          video = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}</div>`}` : ``}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  Example as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,yiBAAyiB;AACjjB,EAAE,GAAG,EAAE,u9EAAu9E;AAC99E,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,UAAU,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE;AAC9C,MAAM,0BAA0B;AAChC,MAAM,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC;AAC1H,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACtE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG;AAC7B,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,IAAI;AACZ,QAAQ,IAAI,EAAE,KAAK;AACnB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,KAAK,GAAG,OAAO,CAAC;AAC1B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}