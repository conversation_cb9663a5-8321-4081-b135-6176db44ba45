{"version": 3, "file": "ssr-Cwm06D-i.js", "sources": ["../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/utils.js", "../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/dom.js", "../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/lifecycle.js", "../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/each.js", "../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/shared/boolean_attributes.js", "../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/ssr.js"], "sourcesContent": ["/** @returns {void} */\nexport function noop() {}\n\nexport const identity = (x) => x;\n\n/**\n * @template T\n * @template S\n * @param {T} tar\n * @param {S} src\n * @returns {T & S}\n */\nexport function assign(tar, src) {\n\t// @ts-ignore\n\tfor (const k in src) tar[k] = src[k];\n\treturn /** @type {T & S} */ (tar);\n}\n\n// Adapted from https://github.com/then/is-promise/blob/master/index.js\n// Distributed under MIT License https://github.com/then/is-promise/blob/master/LICENSE\n/**\n * @param {any} value\n * @returns {value is PromiseLike<any>}\n */\nexport function is_promise(value) {\n\treturn (\n\t\t!!value &&\n\t\t(typeof value === 'object' || typeof value === 'function') &&\n\t\ttypeof (/** @type {any} */ (value).then) === 'function'\n\t);\n}\n\n/** @returns {void} */\nexport function add_location(element, file, line, column, char) {\n\telement.__svelte_meta = {\n\t\tloc: { file, line, column, char }\n\t};\n}\n\nexport function run(fn) {\n\treturn fn();\n}\n\nexport function blank_object() {\n\treturn Object.create(null);\n}\n\n/**\n * @param {Function[]} fns\n * @returns {void}\n */\nexport function run_all(fns) {\n\tfns.forEach(run);\n}\n\n/**\n * @param {any} thing\n * @returns {thing is Function}\n */\nexport function is_function(thing) {\n\treturn typeof thing === 'function';\n}\n\n/** @returns {boolean} */\nexport function safe_not_equal(a, b) {\n\treturn a != a ? b == b : a !== b || (a && typeof a === 'object') || typeof a === 'function';\n}\n\nlet src_url_equal_anchor;\n\n/**\n * @param {string} element_src\n * @param {string} url\n * @returns {boolean}\n */\nexport function src_url_equal(element_src, url) {\n\tif (element_src === url) return true;\n\tif (!src_url_equal_anchor) {\n\t\tsrc_url_equal_anchor = document.createElement('a');\n\t}\n\t// This is actually faster than doing URL(..).href\n\tsrc_url_equal_anchor.href = url;\n\treturn element_src === src_url_equal_anchor.href;\n}\n\n/** @param {string} srcset */\nfunction split_srcset(srcset) {\n\treturn srcset.split(',').map((src) => src.trim().split(' ').filter(Boolean));\n}\n\n/**\n * @param {HTMLSourceElement | HTMLImageElement} element_srcset\n * @param {string | undefined | null} srcset\n * @returns {boolean}\n */\nexport function srcset_url_equal(element_srcset, srcset) {\n\tconst element_urls = split_srcset(element_srcset.srcset);\n\tconst urls = split_srcset(srcset || '');\n\n\treturn (\n\t\turls.length === element_urls.length &&\n\t\turls.every(\n\t\t\t([url, width], i) =>\n\t\t\t\twidth === element_urls[i][1] &&\n\t\t\t\t// We need to test both ways because Vite will create an a full URL with\n\t\t\t\t// `new URL(asset, import.meta.url).href` for the client when `base: './'`, and the\n\t\t\t\t// relative URLs inside srcset are not automatically resolved to absolute URLs by\n\t\t\t\t// browsers (in contrast to img.src). This means both SSR and DOM code could\n\t\t\t\t// contain relative or absolute URLs.\n\t\t\t\t(src_url_equal(element_urls[i][0], url) || src_url_equal(url, element_urls[i][0]))\n\t\t)\n\t);\n}\n\n/** @returns {boolean} */\nexport function not_equal(a, b) {\n\treturn a != a ? b == b : a !== b;\n}\n\n/** @returns {boolean} */\nexport function is_empty(obj) {\n\treturn Object.keys(obj).length === 0;\n}\n\n/** @returns {void} */\nexport function validate_store(store, name) {\n\tif (store != null && typeof store.subscribe !== 'function') {\n\t\tthrow new Error(`'${name}' is not a store with a 'subscribe' method`);\n\t}\n}\n\nexport function subscribe(store, ...callbacks) {\n\tif (store == null) {\n\t\tfor (const callback of callbacks) {\n\t\t\tcallback(undefined);\n\t\t}\n\t\treturn noop;\n\t}\n\tconst unsub = store.subscribe(...callbacks);\n\treturn unsub.unsubscribe ? () => unsub.unsubscribe() : unsub;\n}\n\n/**\n * Get the current value from a store by subscribing and immediately unsubscribing.\n *\n * https://svelte.dev/docs/svelte-store#get\n * @template T\n * @param {import('../store/public.js').Readable<T>} store\n * @returns {T}\n */\nexport function get_store_value(store) {\n\tlet value;\n\tsubscribe(store, (_) => (value = _))();\n\treturn value;\n}\n\n/** @returns {void} */\nexport function component_subscribe(component, store, callback) {\n\tcomponent.$$.on_destroy.push(subscribe(store, callback));\n}\n\nexport function create_slot(definition, ctx, $$scope, fn) {\n\tif (definition) {\n\t\tconst slot_ctx = get_slot_context(definition, ctx, $$scope, fn);\n\t\treturn definition[0](slot_ctx);\n\t}\n}\n\nfunction get_slot_context(definition, ctx, $$scope, fn) {\n\treturn definition[1] && fn ? assign($$scope.ctx.slice(), definition[1](fn(ctx))) : $$scope.ctx;\n}\n\nexport function get_slot_changes(definition, $$scope, dirty, fn) {\n\tif (definition[2] && fn) {\n\t\tconst lets = definition[2](fn(dirty));\n\t\tif ($$scope.dirty === undefined) {\n\t\t\treturn lets;\n\t\t}\n\t\tif (typeof lets === 'object') {\n\t\t\tconst merged = [];\n\t\t\tconst len = Math.max($$scope.dirty.length, lets.length);\n\t\t\tfor (let i = 0; i < len; i += 1) {\n\t\t\t\tmerged[i] = $$scope.dirty[i] | lets[i];\n\t\t\t}\n\t\t\treturn merged;\n\t\t}\n\t\treturn $$scope.dirty | lets;\n\t}\n\treturn $$scope.dirty;\n}\n\n/** @returns {void} */\nexport function update_slot_base(\n\tslot,\n\tslot_definition,\n\tctx,\n\t$$scope,\n\tslot_changes,\n\tget_slot_context_fn\n) {\n\tif (slot_changes) {\n\t\tconst slot_context = get_slot_context(slot_definition, ctx, $$scope, get_slot_context_fn);\n\t\tslot.p(slot_context, slot_changes);\n\t}\n}\n\n/** @returns {void} */\nexport function update_slot(\n\tslot,\n\tslot_definition,\n\tctx,\n\t$$scope,\n\tdirty,\n\tget_slot_changes_fn,\n\tget_slot_context_fn\n) {\n\tconst slot_changes = get_slot_changes(slot_definition, $$scope, dirty, get_slot_changes_fn);\n\tupdate_slot_base(slot, slot_definition, ctx, $$scope, slot_changes, get_slot_context_fn);\n}\n\n/** @returns {any[] | -1} */\nexport function get_all_dirty_from_scope($$scope) {\n\tif ($$scope.ctx.length > 32) {\n\t\tconst dirty = [];\n\t\tconst length = $$scope.ctx.length / 32;\n\t\tfor (let i = 0; i < length; i++) {\n\t\t\tdirty[i] = -1;\n\t\t}\n\t\treturn dirty;\n\t}\n\treturn -1;\n}\n\n/** @returns {{}} */\nexport function exclude_internal_props(props) {\n\tconst result = {};\n\tfor (const k in props) if (k[0] !== '$') result[k] = props[k];\n\treturn result;\n}\n\n/** @returns {{}} */\nexport function compute_rest_props(props, keys) {\n\tconst rest = {};\n\tkeys = new Set(keys);\n\tfor (const k in props) if (!keys.has(k) && k[0] !== '$') rest[k] = props[k];\n\treturn rest;\n}\n\n/** @returns {{}} */\nexport function compute_slots(slots) {\n\tconst result = {};\n\tfor (const key in slots) {\n\t\tresult[key] = true;\n\t}\n\treturn result;\n}\n\n/** @returns {(this: any, ...args: any[]) => void} */\nexport function once(fn) {\n\tlet ran = false;\n\treturn function (...args) {\n\t\tif (ran) return;\n\t\tran = true;\n\t\tfn.call(this, ...args);\n\t};\n}\n\nexport function null_to_empty(value) {\n\treturn value == null ? '' : value;\n}\n\nexport function set_store_value(store, ret, value) {\n\tstore.set(value);\n\treturn ret;\n}\n\nexport const has_prop = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);\n\nexport function action_destroyer(action_result) {\n\treturn action_result && is_function(action_result.destroy) ? action_result.destroy : noop;\n}\n\n/** @param {number | string} value\n * @returns {[number, string]}\n */\nexport function split_css_unit(value) {\n\tconst split = typeof value === 'string' && value.match(/^\\s*(-?[\\d.]+)([^\\s]*)\\s*$/);\n\treturn split ? [parseFloat(split[1]), split[2] || 'px'] : [/** @type {number} */ (value), 'px'];\n}\n\nexport const contenteditable_truthy_values = ['', true, 1, 'true', 'contenteditable'];\n", "import { contenteditable_truthy_values, has_prop } from './utils.js';\n\nimport { ResizeObserverSingleton } from './ResizeObserverSingleton.js';\n\n// Track which nodes are claimed during hydration. Unclaimed nodes can then be removed from the DOM\n// at the end of hydration without touching the remaining nodes.\nlet is_hydrating = false;\n\n/**\n * @returns {void}\n */\nexport function start_hydrating() {\n\tis_hydrating = true;\n}\n\n/**\n * @returns {void}\n */\nexport function end_hydrating() {\n\tis_hydrating = false;\n}\n\n/**\n * @param {number} low\n * @param {number} high\n * @param {(index: number) => number} key\n * @param {number} value\n * @returns {number}\n */\nfunction upper_bound(low, high, key, value) {\n\t// Return first index of value larger than input value in the range [low, high)\n\twhile (low < high) {\n\t\tconst mid = low + ((high - low) >> 1);\n\t\tif (key(mid) <= value) {\n\t\t\tlow = mid + 1;\n\t\t} else {\n\t\t\thigh = mid;\n\t\t}\n\t}\n\treturn low;\n}\n\n/**\n * @param {NodeEx} target\n * @returns {void}\n */\nfunction init_hydrate(target) {\n\tif (target.hydrate_init) return;\n\ttarget.hydrate_init = true;\n\t// We know that all children have claim_order values since the unclaimed have been detached if target is not <head>\n\n\tlet children = /** @type {ArrayLike<NodeEx2>} */ (target.childNodes);\n\t// If target is <head>, there may be children without claim_order\n\tif (target.nodeName === 'HEAD') {\n\t\tconst my_children = [];\n\t\tfor (let i = 0; i < children.length; i++) {\n\t\t\tconst node = children[i];\n\t\t\tif (node.claim_order !== undefined) {\n\t\t\t\tmy_children.push(node);\n\t\t\t}\n\t\t}\n\t\tchildren = my_children;\n\t}\n\t/*\n\t * Reorder claimed children optimally.\n\t * We can reorder claimed children optimally by finding the longest subsequence of\n\t * nodes that are already claimed in order and only moving the rest. The longest\n\t * subsequence of nodes that are claimed in order can be found by\n\t * computing the longest increasing subsequence of .claim_order values.\n\t *\n\t * This algorithm is optimal in generating the least amount of reorder operations\n\t * possible.\n\t *\n\t * Proof:\n\t * We know that, given a set of reordering operations, the nodes that do not move\n\t * always form an increasing subsequence, since they do not move among each other\n\t * meaning that they must be already ordered among each other. Thus, the maximal\n\t * set of nodes that do not move form a longest increasing subsequence.\n\t */\n\t// Compute longest increasing subsequence\n\t// m: subsequence length j => index k of smallest value that ends an increasing subsequence of length j\n\tconst m = new Int32Array(children.length + 1);\n\t// Predecessor indices + 1\n\tconst p = new Int32Array(children.length);\n\tm[0] = -1;\n\tlet longest = 0;\n\tfor (let i = 0; i < children.length; i++) {\n\t\tconst current = children[i].claim_order;\n\t\t// Find the largest subsequence length such that it ends in a value less than our current value\n\t\t// upper_bound returns first greater value, so we subtract one\n\t\t// with fast path for when we are on the current longest subsequence\n\t\tconst seq_len =\n\t\t\t(longest > 0 && children[m[longest]].claim_order <= current\n\t\t\t\t? longest + 1\n\t\t\t\t: upper_bound(1, longest, (idx) => children[m[idx]].claim_order, current)) - 1;\n\t\tp[i] = m[seq_len] + 1;\n\t\tconst new_len = seq_len + 1;\n\t\t// We can guarantee that current is the smallest value. Otherwise, we would have generated a longer sequence.\n\t\tm[new_len] = i;\n\t\tlongest = Math.max(new_len, longest);\n\t}\n\t// The longest increasing subsequence of nodes (initially reversed)\n\n\t/**\n\t * @type {NodeEx2[]}\n\t */\n\tconst lis = [];\n\t// The rest of the nodes, nodes that will be moved\n\n\t/**\n\t * @type {NodeEx2[]}\n\t */\n\tconst to_move = [];\n\tlet last = children.length - 1;\n\tfor (let cur = m[longest] + 1; cur != 0; cur = p[cur - 1]) {\n\t\tlis.push(children[cur - 1]);\n\t\tfor (; last >= cur; last--) {\n\t\t\tto_move.push(children[last]);\n\t\t}\n\t\tlast--;\n\t}\n\tfor (; last >= 0; last--) {\n\t\tto_move.push(children[last]);\n\t}\n\tlis.reverse();\n\t// We sort the nodes being moved to guarantee that their insertion order matches the claim order\n\tto_move.sort((a, b) => a.claim_order - b.claim_order);\n\t// Finally, we move the nodes\n\tfor (let i = 0, j = 0; i < to_move.length; i++) {\n\t\twhile (j < lis.length && to_move[i].claim_order >= lis[j].claim_order) {\n\t\t\tj++;\n\t\t}\n\t\tconst anchor = j < lis.length ? lis[j] : null;\n\t\ttarget.insertBefore(to_move[i], anchor);\n\t}\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @returns {void}\n */\nexport function append(target, node) {\n\ttarget.appendChild(node);\n}\n\n/**\n * @param {Node} target\n * @param {string} style_sheet_id\n * @param {string} styles\n * @returns {void}\n */\nexport function append_styles(target, style_sheet_id, styles) {\n\tconst append_styles_to = get_root_for_style(target);\n\tif (!append_styles_to.getElementById(style_sheet_id)) {\n\t\tconst style = element('style');\n\t\tstyle.id = style_sheet_id;\n\t\tstyle.textContent = styles;\n\t\tappend_stylesheet(append_styles_to, style);\n\t}\n}\n\n/**\n * @param {Node} node\n * @returns {ShadowRoot | Document}\n */\nexport function get_root_for_style(node) {\n\tif (!node) return document;\n\tconst root = node.getRootNode ? node.getRootNode() : node.ownerDocument;\n\tif (root && /** @type {ShadowRoot} */ (root).host) {\n\t\treturn /** @type {ShadowRoot} */ (root);\n\t}\n\treturn node.ownerDocument;\n}\n\n/**\n * @param {Node} node\n * @returns {CSSStyleSheet}\n */\nexport function append_empty_stylesheet(node) {\n\tconst style_element = element('style');\n\t// For transitions to work without 'style-src: unsafe-inline' Content Security Policy,\n\t// these empty tags need to be allowed with a hash as a workaround until we move to the Web Animations API.\n\t// Using the hash for the empty string (for an empty tag) works in all browsers except Safari.\n\t// So as a workaround for the workaround, when we append empty style tags we set their content to /* empty */.\n\t// The hash 'sha256-9OlNO0DNEeaVzHL4RZwCLsBHA8WBQ8toBp/4F5XV2nc=' will then work even in Safari.\n\tstyle_element.textContent = '/* empty */';\n\tappend_stylesheet(get_root_for_style(node), style_element);\n\treturn style_element.sheet;\n}\n\n/**\n * @param {ShadowRoot | Document} node\n * @param {HTMLStyleElement} style\n * @returns {CSSStyleSheet}\n */\nfunction append_stylesheet(node, style) {\n\tappend(/** @type {Document} */ (node).head || node, style);\n\treturn style.sheet;\n}\n\n/**\n * @param {NodeEx} target\n * @param {NodeEx} node\n * @returns {void}\n */\nexport function append_hydration(target, node) {\n\tif (is_hydrating) {\n\t\tinit_hydrate(target);\n\t\tif (\n\t\t\ttarget.actual_end_child === undefined ||\n\t\t\t(target.actual_end_child !== null && target.actual_end_child.parentNode !== target)\n\t\t) {\n\t\t\ttarget.actual_end_child = target.firstChild;\n\t\t}\n\t\t// Skip nodes of undefined ordering\n\t\twhile (target.actual_end_child !== null && target.actual_end_child.claim_order === undefined) {\n\t\t\ttarget.actual_end_child = target.actual_end_child.nextSibling;\n\t\t}\n\t\tif (node !== target.actual_end_child) {\n\t\t\t// We only insert if the ordering of this node should be modified or the parent node is not target\n\t\t\tif (node.claim_order !== undefined || node.parentNode !== target) {\n\t\t\t\ttarget.insertBefore(node, target.actual_end_child);\n\t\t\t}\n\t\t} else {\n\t\t\ttarget.actual_end_child = node.nextSibling;\n\t\t}\n\t} else if (node.parentNode !== target || node.nextSibling !== null) {\n\t\ttarget.appendChild(node);\n\t}\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @param {Node} [anchor]\n * @returns {void}\n */\nexport function insert(target, node, anchor) {\n\ttarget.insertBefore(node, anchor || null);\n}\n\n/**\n * @param {NodeEx} target\n * @param {NodeEx} node\n * @param {NodeEx} [anchor]\n * @returns {void}\n */\nexport function insert_hydration(target, node, anchor) {\n\tif (is_hydrating && !anchor) {\n\t\tappend_hydration(target, node);\n\t} else if (node.parentNode !== target || node.nextSibling != anchor) {\n\t\ttarget.insertBefore(node, anchor || null);\n\t}\n}\n\n/**\n * @param {Node} node\n * @returns {void}\n */\nexport function detach(node) {\n\tif (node.parentNode) {\n\t\tnode.parentNode.removeChild(node);\n\t}\n}\n\n/**\n * @returns {void} */\nexport function destroy_each(iterations, detaching) {\n\tfor (let i = 0; i < iterations.length; i += 1) {\n\t\tif (iterations[i]) iterations[i].d(detaching);\n\t}\n}\n\n/**\n * @template {keyof HTMLElementTagNameMap} K\n * @param {K} name\n * @returns {HTMLElementTagNameMap[K]}\n */\nexport function element(name) {\n\treturn document.createElement(name);\n}\n\n/**\n * @template {keyof HTMLElementTagNameMap} K\n * @param {K} name\n * @param {string} is\n * @returns {HTMLElementTagNameMap[K]}\n */\nexport function element_is(name, is) {\n\treturn document.createElement(name, { is });\n}\n\n/**\n * @template T\n * @template {keyof T} K\n * @param {T} obj\n * @param {K[]} exclude\n * @returns {Pick<T, Exclude<keyof T, K>>}\n */\nexport function object_without_properties(obj, exclude) {\n\tconst target = /** @type {Pick<T, Exclude<keyof T, K>>} */ ({});\n\tfor (const k in obj) {\n\t\tif (\n\t\t\thas_prop(obj, k) &&\n\t\t\t// @ts-ignore\n\t\t\texclude.indexOf(k) === -1\n\t\t) {\n\t\t\t// @ts-ignore\n\t\t\ttarget[k] = obj[k];\n\t\t}\n\t}\n\treturn target;\n}\n\n/**\n * @template {keyof SVGElementTagNameMap} K\n * @param {K} name\n * @returns {SVGElement}\n */\nexport function svg_element(name) {\n\treturn document.createElementNS('http://www.w3.org/2000/svg', name);\n}\n\n/**\n * @param {string} data\n * @returns {Text}\n */\nexport function text(data) {\n\treturn document.createTextNode(data);\n}\n\n/**\n * @returns {Text} */\nexport function space() {\n\treturn text(' ');\n}\n\n/**\n * @returns {Text} */\nexport function empty() {\n\treturn text('');\n}\n\n/**\n * @param {string} content\n * @returns {Comment}\n */\nexport function comment(content) {\n\treturn document.createComment(content);\n}\n\n/**\n * @param {EventTarget} node\n * @param {string} event\n * @param {EventListenerOrEventListenerObject} handler\n * @param {boolean | AddEventListenerOptions | EventListenerOptions} [options]\n * @returns {() => void}\n */\nexport function listen(node, event, handler, options) {\n\tnode.addEventListener(event, handler, options);\n\treturn () => node.removeEventListener(event, handler, options);\n}\n\n/**\n * @returns {(event: any) => any} */\nexport function prevent_default(fn) {\n\treturn function (event) {\n\t\tevent.preventDefault();\n\t\t// @ts-ignore\n\t\treturn fn.call(this, event);\n\t};\n}\n\n/**\n * @returns {(event: any) => any} */\nexport function stop_propagation(fn) {\n\treturn function (event) {\n\t\tevent.stopPropagation();\n\t\t// @ts-ignore\n\t\treturn fn.call(this, event);\n\t};\n}\n\n/**\n * @returns {(event: any) => any} */\nexport function stop_immediate_propagation(fn) {\n\treturn function (event) {\n\t\tevent.stopImmediatePropagation();\n\t\t// @ts-ignore\n\t\treturn fn.call(this, event);\n\t};\n}\n\n/**\n * @returns {(event: any) => void} */\nexport function self(fn) {\n\treturn function (event) {\n\t\t// @ts-ignore\n\t\tif (event.target === this) fn.call(this, event);\n\t};\n}\n\n/**\n * @returns {(event: any) => void} */\nexport function trusted(fn) {\n\treturn function (event) {\n\t\t// @ts-ignore\n\t\tif (event.isTrusted) fn.call(this, event);\n\t};\n}\n\n/**\n * @param {Element} node\n * @param {string} attribute\n * @param {string} [value]\n * @returns {void}\n */\nexport function attr(node, attribute, value) {\n\tif (value == null) node.removeAttribute(attribute);\n\telse if (node.getAttribute(attribute) !== value) node.setAttribute(attribute, value);\n}\n/**\n * List of attributes that should always be set through the attr method,\n * because updating them through the property setter doesn't work reliably.\n * In the example of `width`/`height`, the problem is that the setter only\n * accepts numeric values, but the attribute can also be set to a string like `50%`.\n * If this list becomes too big, rethink this approach.\n */\nconst always_set_through_set_attribute = ['width', 'height'];\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {{ [x: string]: string }} attributes\n * @returns {void}\n */\nexport function set_attributes(node, attributes) {\n\t// @ts-ignore\n\tconst descriptors = Object.getOwnPropertyDescriptors(node.__proto__);\n\tfor (const key in attributes) {\n\t\tif (attributes[key] == null) {\n\t\t\tnode.removeAttribute(key);\n\t\t} else if (key === 'style') {\n\t\t\tnode.style.cssText = attributes[key];\n\t\t} else if (key === '__value') {\n\t\t\t/** @type {any} */ (node).value = node[key] = attributes[key];\n\t\t} else if (\n\t\t\tdescriptors[key] &&\n\t\t\tdescriptors[key].set &&\n\t\t\talways_set_through_set_attribute.indexOf(key) === -1\n\t\t) {\n\t\t\tnode[key] = attributes[key];\n\t\t} else {\n\t\t\tattr(node, key, attributes[key]);\n\t\t}\n\t}\n}\n\n/**\n * @param {Element & ElementCSSInlineStyle} node\n * @param {{ [x: string]: string }} attributes\n * @returns {void}\n */\nexport function set_svg_attributes(node, attributes) {\n\tfor (const key in attributes) {\n\t\tattr(node, key, attributes[key]);\n\t}\n}\n\n/**\n * @param {Record<string, unknown>} data_map\n * @returns {void}\n */\nexport function set_custom_element_data_map(node, data_map) {\n\tObject.keys(data_map).forEach((key) => {\n\t\tset_custom_element_data(node, key, data_map[key]);\n\t});\n}\n\n/**\n * @returns {void} */\nexport function set_custom_element_data(node, prop, value) {\n\tconst lower = prop.toLowerCase(); // for backwards compatibility with existing behavior we do lowercase first\n\tif (lower in node) {\n\t\tnode[lower] = typeof node[lower] === 'boolean' && value === '' ? true : value;\n\t} else if (prop in node) {\n\t\tnode[prop] = typeof node[prop] === 'boolean' && value === '' ? true : value;\n\t} else {\n\t\tattr(node, prop, value);\n\t}\n}\n\n/**\n * @param {string} tag\n */\nexport function set_dynamic_element_data(tag) {\n\treturn /-/.test(tag) ? set_custom_element_data_map : set_attributes;\n}\n\n/**\n * @returns {void}\n */\nexport function xlink_attr(node, attribute, value) {\n\tnode.setAttributeNS('http://www.w3.org/1999/xlink', attribute, value);\n}\n\n/**\n * @param {HTMLElement} node\n * @returns {string}\n */\nexport function get_svelte_dataset(node) {\n\treturn node.dataset.svelteH;\n}\n\n/**\n * @returns {unknown[]} */\nexport function get_binding_group_value(group, __value, checked) {\n\tconst value = new Set();\n\tfor (let i = 0; i < group.length; i += 1) {\n\t\tif (group[i].checked) value.add(group[i].__value);\n\t}\n\tif (!checked) {\n\t\tvalue.delete(__value);\n\t}\n\treturn Array.from(value);\n}\n\n/**\n * @param {HTMLInputElement[]} group\n * @returns {{ p(...inputs: HTMLInputElement[]): void; r(): void; }}\n */\nexport function init_binding_group(group) {\n\t/**\n\t * @type {HTMLInputElement[]} */\n\tlet _inputs;\n\treturn {\n\t\t/* push */ p(...inputs) {\n\t\t\t_inputs = inputs;\n\t\t\t_inputs.forEach((input) => group.push(input));\n\t\t},\n\t\t/* remove */ r() {\n\t\t\t_inputs.forEach((input) => group.splice(group.indexOf(input), 1));\n\t\t}\n\t};\n}\n\n/**\n * @param {number[]} indexes\n * @returns {{ u(new_indexes: number[]): void; p(...inputs: HTMLInputElement[]): void; r: () => void; }}\n */\nexport function init_binding_group_dynamic(group, indexes) {\n\t/**\n\t * @type {HTMLInputElement[]} */\n\tlet _group = get_binding_group(group);\n\n\t/**\n\t * @type {HTMLInputElement[]} */\n\tlet _inputs;\n\n\tfunction get_binding_group(group) {\n\t\tfor (let i = 0; i < indexes.length; i++) {\n\t\t\tgroup = group[indexes[i]] = group[indexes[i]] || [];\n\t\t}\n\t\treturn group;\n\t}\n\n\t/**\n\t * @returns {void} */\n\tfunction push() {\n\t\t_inputs.forEach((input) => _group.push(input));\n\t}\n\n\t/**\n\t * @returns {void} */\n\tfunction remove() {\n\t\t_inputs.forEach((input) => _group.splice(_group.indexOf(input), 1));\n\t}\n\treturn {\n\t\t/* update */ u(new_indexes) {\n\t\t\tindexes = new_indexes;\n\t\t\tconst new_group = get_binding_group(group);\n\t\t\tif (new_group !== _group) {\n\t\t\t\tremove();\n\t\t\t\t_group = new_group;\n\t\t\t\tpush();\n\t\t\t}\n\t\t},\n\t\t/* push */ p(...inputs) {\n\t\t\t_inputs = inputs;\n\t\t\tpush();\n\t\t},\n\t\t/* remove */ r: remove\n\t};\n}\n\n/** @returns {number} */\nexport function to_number(value) {\n\treturn value === '' ? null : +value;\n}\n\n/** @returns {any[]} */\nexport function time_ranges_to_array(ranges) {\n\tconst array = [];\n\tfor (let i = 0; i < ranges.length; i += 1) {\n\t\tarray.push({ start: ranges.start(i), end: ranges.end(i) });\n\t}\n\treturn array;\n}\n\n/**\n * @param {Element} element\n * @returns {ChildNode[]}\n */\nexport function children(element) {\n\treturn Array.from(element.childNodes);\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @returns {void}\n */\nfunction init_claim_info(nodes) {\n\tif (nodes.claim_info === undefined) {\n\t\tnodes.claim_info = { last_index: 0, total_claimed: 0 };\n\t}\n}\n\n/**\n * @template {ChildNodeEx} R\n * @param {ChildNodeArray} nodes\n * @param {(node: ChildNodeEx) => node is R} predicate\n * @param {(node: ChildNodeEx) => ChildNodeEx | undefined} process_node\n * @param {() => R} create_node\n * @param {boolean} dont_update_last_index\n * @returns {R}\n */\nfunction claim_node(nodes, predicate, process_node, create_node, dont_update_last_index = false) {\n\t// Try to find nodes in an order such that we lengthen the longest increasing subsequence\n\tinit_claim_info(nodes);\n\tconst result_node = (() => {\n\t\t// We first try to find an element after the previous one\n\t\tfor (let i = nodes.claim_info.last_index; i < nodes.length; i++) {\n\t\t\tconst node = nodes[i];\n\t\t\tif (predicate(node)) {\n\t\t\t\tconst replacement = process_node(node);\n\t\t\t\tif (replacement === undefined) {\n\t\t\t\t\tnodes.splice(i, 1);\n\t\t\t\t} else {\n\t\t\t\t\tnodes[i] = replacement;\n\t\t\t\t}\n\t\t\t\tif (!dont_update_last_index) {\n\t\t\t\t\tnodes.claim_info.last_index = i;\n\t\t\t\t}\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\t// Otherwise, we try to find one before\n\t\t// We iterate in reverse so that we don't go too far back\n\t\tfor (let i = nodes.claim_info.last_index - 1; i >= 0; i--) {\n\t\t\tconst node = nodes[i];\n\t\t\tif (predicate(node)) {\n\t\t\t\tconst replacement = process_node(node);\n\t\t\t\tif (replacement === undefined) {\n\t\t\t\t\tnodes.splice(i, 1);\n\t\t\t\t} else {\n\t\t\t\t\tnodes[i] = replacement;\n\t\t\t\t}\n\t\t\t\tif (!dont_update_last_index) {\n\t\t\t\t\tnodes.claim_info.last_index = i;\n\t\t\t\t} else if (replacement === undefined) {\n\t\t\t\t\t// Since we spliced before the last_index, we decrease it\n\t\t\t\t\tnodes.claim_info.last_index--;\n\t\t\t\t}\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\t// If we can't find any matching node, we create a new one\n\t\treturn create_node();\n\t})();\n\tresult_node.claim_order = nodes.claim_info.total_claimed;\n\tnodes.claim_info.total_claimed += 1;\n\treturn result_node;\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @param {string} name\n * @param {{ [key: string]: boolean }} attributes\n * @param {(name: string) => Element | SVGElement} create_element\n * @returns {Element | SVGElement}\n */\nfunction claim_element_base(nodes, name, attributes, create_element) {\n\treturn claim_node(\n\t\tnodes,\n\t\t/** @returns {node is Element | SVGElement} */\n\t\t(node) => node.nodeName === name,\n\t\t/** @param {Element} node */\n\t\t(node) => {\n\t\t\tconst remove = [];\n\t\t\tfor (let j = 0; j < node.attributes.length; j++) {\n\t\t\t\tconst attribute = node.attributes[j];\n\t\t\t\tif (!attributes[attribute.name]) {\n\t\t\t\t\tremove.push(attribute.name);\n\t\t\t\t}\n\t\t\t}\n\t\t\tremove.forEach((v) => node.removeAttribute(v));\n\t\t\treturn undefined;\n\t\t},\n\t\t() => create_element(name)\n\t);\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @param {string} name\n * @param {{ [key: string]: boolean }} attributes\n * @returns {Element | SVGElement}\n */\nexport function claim_element(nodes, name, attributes) {\n\treturn claim_element_base(nodes, name, attributes, element);\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @param {string} name\n * @param {{ [key: string]: boolean }} attributes\n * @returns {Element | SVGElement}\n */\nexport function claim_svg_element(nodes, name, attributes) {\n\treturn claim_element_base(nodes, name, attributes, svg_element);\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @returns {Text}\n */\nexport function claim_text(nodes, data) {\n\treturn claim_node(\n\t\tnodes,\n\t\t/** @returns {node is Text} */\n\t\t(node) => node.nodeType === 3,\n\t\t/** @param {Text} node */\n\t\t(node) => {\n\t\t\tconst data_str = '' + data;\n\t\t\tif (node.data.startsWith(data_str)) {\n\t\t\t\tif (node.data.length !== data_str.length) {\n\t\t\t\t\treturn node.splitText(data_str.length);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tnode.data = data_str;\n\t\t\t}\n\t\t},\n\t\t() => text(data),\n\t\ttrue // Text nodes should not update last index since it is likely not worth it to eliminate an increasing subsequence of actual elements\n\t);\n}\n\n/**\n * @returns {Text} */\nexport function claim_space(nodes) {\n\treturn claim_text(nodes, ' ');\n}\n\n/**\n * @param {ChildNodeArray} nodes\n * @returns {Comment}\n */\nexport function claim_comment(nodes, data) {\n\treturn claim_node(\n\t\tnodes,\n\t\t/** @returns {node is Comment} */\n\t\t(node) => node.nodeType === 8,\n\t\t/** @param {Comment} node */\n\t\t(node) => {\n\t\t\tnode.data = '' + data;\n\t\t\treturn undefined;\n\t\t},\n\t\t() => comment(data),\n\t\ttrue\n\t);\n}\n\nfunction get_comment_idx(nodes, text, start) {\n\tfor (let i = start; i < nodes.length; i += 1) {\n\t\tconst node = nodes[i];\n\t\tif (node.nodeType === 8 /* comment node */ && node.textContent.trim() === text) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n}\n\n/**\n * @param {boolean} is_svg\n * @returns {HtmlTagHydration}\n */\nexport function claim_html_tag(nodes, is_svg) {\n\t// find html opening tag\n\tconst start_index = get_comment_idx(nodes, 'HTML_TAG_START', 0);\n\tconst end_index = get_comment_idx(nodes, 'HTML_TAG_END', start_index + 1);\n\tif (start_index === -1 || end_index === -1) {\n\t\treturn new HtmlTagHydration(is_svg);\n\t}\n\n\tinit_claim_info(nodes);\n\tconst html_tag_nodes = nodes.splice(start_index, end_index - start_index + 1);\n\tdetach(html_tag_nodes[0]);\n\tdetach(html_tag_nodes[html_tag_nodes.length - 1]);\n\tconst claimed_nodes = html_tag_nodes.slice(1, html_tag_nodes.length - 1);\n\tif (claimed_nodes.length === 0) {\n\t\treturn new HtmlTagHydration(is_svg);\n\t}\n\tfor (const n of claimed_nodes) {\n\t\tn.claim_order = nodes.claim_info.total_claimed;\n\t\tnodes.claim_info.total_claimed += 1;\n\t}\n\treturn new HtmlTagHydration(is_svg, claimed_nodes);\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @returns {void}\n */\nexport function set_data(text, data) {\n\tdata = '' + data;\n\tif (text.data === data) return;\n\ttext.data = /** @type {string} */ (data);\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @returns {void}\n */\nexport function set_data_contenteditable(text, data) {\n\tdata = '' + data;\n\tif (text.wholeText === data) return;\n\ttext.data = /** @type {string} */ (data);\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @param {string} attr_value\n * @returns {void}\n */\nexport function set_data_maybe_contenteditable(text, data, attr_value) {\n\tif (~contenteditable_truthy_values.indexOf(attr_value)) {\n\t\tset_data_contenteditable(text, data);\n\t} else {\n\t\tset_data(text, data);\n\t}\n}\n\n/**\n * @returns {void} */\nexport function set_input_value(input, value) {\n\tinput.value = value == null ? '' : value;\n}\n\n/**\n * @returns {void} */\nexport function set_input_type(input, type) {\n\ttry {\n\t\tinput.type = type;\n\t} catch (e) {\n\t\t// do nothing\n\t}\n}\n\n/**\n * @returns {void} */\nexport function set_style(node, key, value, important) {\n\tif (value == null) {\n\t\tnode.style.removeProperty(key);\n\t} else {\n\t\tnode.style.setProperty(key, value, important ? 'important' : '');\n\t}\n}\n\n/**\n * @returns {void} */\nexport function select_option(select, value, mounting) {\n\tfor (let i = 0; i < select.options.length; i += 1) {\n\t\tconst option = select.options[i];\n\t\tif (option.__value === value) {\n\t\t\toption.selected = true;\n\t\t\treturn;\n\t\t}\n\t}\n\tif (!mounting || value !== undefined) {\n\t\tselect.selectedIndex = -1; // no option should be selected\n\t}\n}\n\n/**\n * @returns {void} */\nexport function select_options(select, value) {\n\tfor (let i = 0; i < select.options.length; i += 1) {\n\t\tconst option = select.options[i];\n\t\toption.selected = ~value.indexOf(option.__value);\n\t}\n}\n\nexport function select_value(select) {\n\tconst selected_option = select.querySelector(':checked');\n\treturn selected_option && selected_option.__value;\n}\n\nexport function select_multiple_value(select) {\n\treturn [].map.call(select.querySelectorAll(':checked'), (option) => option.__value);\n}\n// unfortunately this can't be a constant as that wouldn't be tree-shakeable\n// so we cache the result instead\n\n/**\n * @type {boolean} */\nlet crossorigin;\n\n/**\n * @returns {boolean} */\nexport function is_crossorigin() {\n\tif (crossorigin === undefined) {\n\t\tcrossorigin = false;\n\t\ttry {\n\t\t\tif (typeof window !== 'undefined' && window.parent) {\n\t\t\t\tvoid window.parent.document;\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tcrossorigin = true;\n\t\t}\n\t}\n\treturn crossorigin;\n}\n\n/**\n * @param {HTMLElement} node\n * @param {() => void} fn\n * @returns {() => void}\n */\nexport function add_iframe_resize_listener(node, fn) {\n\tconst computed_style = getComputedStyle(node);\n\tif (computed_style.position === 'static') {\n\t\tnode.style.position = 'relative';\n\t}\n\tconst iframe = element('iframe');\n\tiframe.setAttribute(\n\t\t'style',\n\t\t'display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; ' +\n\t\t\t'overflow: hidden; border: 0; opacity: 0; pointer-events: none; z-index: -1;'\n\t);\n\tiframe.setAttribute('aria-hidden', 'true');\n\tiframe.tabIndex = -1;\n\tconst crossorigin = is_crossorigin();\n\n\t/**\n\t * @type {() => void}\n\t */\n\tlet unsubscribe;\n\tif (crossorigin) {\n\t\tiframe.src = \"data:text/html,<script>onresize=function(){parent.postMessage(0,'*')}</script>\";\n\t\tunsubscribe = listen(\n\t\t\twindow,\n\t\t\t'message',\n\t\t\t/** @param {MessageEvent} event */ (event) => {\n\t\t\t\tif (event.source === iframe.contentWindow) fn();\n\t\t\t}\n\t\t);\n\t} else {\n\t\tiframe.src = 'about:blank';\n\t\tiframe.onload = () => {\n\t\t\tunsubscribe = listen(iframe.contentWindow, 'resize', fn);\n\t\t\t// make sure an initial resize event is fired _after_ the iframe is loaded (which is asynchronous)\n\t\t\t// see https://github.com/sveltejs/svelte/issues/4233\n\t\t\tfn();\n\t\t};\n\t}\n\tappend(node, iframe);\n\treturn () => {\n\t\tif (crossorigin) {\n\t\t\tunsubscribe();\n\t\t} else if (unsubscribe && iframe.contentWindow) {\n\t\t\tunsubscribe();\n\t\t}\n\t\tdetach(iframe);\n\t};\n}\nexport const resize_observer_content_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'content-box'\n});\nexport const resize_observer_border_box = /* @__PURE__ */ new ResizeObserverSingleton({\n\tbox: 'border-box'\n});\nexport const resize_observer_device_pixel_content_box = /* @__PURE__ */ new ResizeObserverSingleton(\n\t{ box: 'device-pixel-content-box' }\n);\nexport { ResizeObserverSingleton };\n\n/**\n * @returns {void} */\nexport function toggle_class(element, name, toggle) {\n\t// The `!!` is required because an `undefined` flag means flipping the current state.\n\telement.classList.toggle(name, !!toggle);\n}\n\n/**\n * @template T\n * @param {string} type\n * @param {T} [detail]\n * @param {{ bubbles?: boolean, cancelable?: boolean }} [options]\n * @returns {CustomEvent<T>}\n */\nexport function custom_event(type, detail, { bubbles = false, cancelable = false } = {}) {\n\treturn new CustomEvent(type, { detail, bubbles, cancelable });\n}\n\n/**\n * @param {string} selector\n * @param {HTMLElement} parent\n * @returns {ChildNodeArray}\n */\nexport function query_selector_all(selector, parent = document.body) {\n\treturn Array.from(parent.querySelectorAll(selector));\n}\n\n/**\n * @param {string} nodeId\n * @param {HTMLElement} head\n * @returns {any[]}\n */\nexport function head_selector(nodeId, head) {\n\tconst result = [];\n\tlet started = 0;\n\tfor (const node of head.childNodes) {\n\t\tif (node.nodeType === 8 /* comment node */) {\n\t\t\tconst comment = node.textContent.trim();\n\t\t\tif (comment === `HEAD_${nodeId}_END`) {\n\t\t\t\tstarted -= 1;\n\t\t\t\tresult.push(node);\n\t\t\t} else if (comment === `HEAD_${nodeId}_START`) {\n\t\t\t\tstarted += 1;\n\t\t\t\tresult.push(node);\n\t\t\t}\n\t\t} else if (started > 0) {\n\t\t\tresult.push(node);\n\t\t}\n\t}\n\treturn result;\n}\n/** */\nexport class HtmlTag {\n\t/**\n\t * @private\n\t * @default false\n\t */\n\tis_svg = false;\n\t/** parent for creating node */\n\te = undefined;\n\t/** html tag nodes */\n\tn = undefined;\n\t/** target */\n\tt = undefined;\n\t/** anchor */\n\ta = undefined;\n\tconstructor(is_svg = false) {\n\t\tthis.is_svg = is_svg;\n\t\tthis.e = this.n = null;\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @returns {void}\n\t */\n\tc(html) {\n\t\tthis.h(html);\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @param {HTMLElement | SVGElement} target\n\t * @param {HTMLElement | SVGElement} anchor\n\t * @returns {void}\n\t */\n\tm(html, target, anchor = null) {\n\t\tif (!this.e) {\n\t\t\tif (this.is_svg)\n\t\t\t\tthis.e = svg_element(/** @type {keyof SVGElementTagNameMap} */ (target.nodeName));\n\t\t\t/** #7364  target for <template> may be provided as #document-fragment(11) */ else\n\t\t\t\tthis.e = element(\n\t\t\t\t\t/** @type {keyof HTMLElementTagNameMap} */ (\n\t\t\t\t\t\ttarget.nodeType === 11 ? 'TEMPLATE' : target.nodeName\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t\tthis.t =\n\t\t\t\ttarget.tagName !== 'TEMPLATE'\n\t\t\t\t\t? target\n\t\t\t\t\t: /** @type {HTMLTemplateElement} */ (target).content;\n\t\t\tthis.c(html);\n\t\t}\n\t\tthis.i(anchor);\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @returns {void}\n\t */\n\th(html) {\n\t\tthis.e.innerHTML = html;\n\t\tthis.n = Array.from(\n\t\t\tthis.e.nodeName === 'TEMPLATE' ? this.e.content.childNodes : this.e.childNodes\n\t\t);\n\t}\n\n\t/**\n\t * @returns {void} */\n\ti(anchor) {\n\t\tfor (let i = 0; i < this.n.length; i += 1) {\n\t\t\tinsert(this.t, this.n[i], anchor);\n\t\t}\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @returns {void}\n\t */\n\tp(html) {\n\t\tthis.d();\n\t\tthis.h(html);\n\t\tthis.i(this.a);\n\t}\n\n\t/**\n\t * @returns {void} */\n\td() {\n\t\tthis.n.forEach(detach);\n\t}\n}\n\nexport class HtmlTagHydration extends HtmlTag {\n\t/** @type {Element[]} hydration claimed nodes */\n\tl = undefined;\n\n\tconstructor(is_svg = false, claimed_nodes) {\n\t\tsuper(is_svg);\n\t\tthis.e = this.n = null;\n\t\tthis.l = claimed_nodes;\n\t}\n\n\t/**\n\t * @param {string} html\n\t * @returns {void}\n\t */\n\tc(html) {\n\t\tif (this.l) {\n\t\t\tthis.n = this.l;\n\t\t} else {\n\t\t\tsuper.c(html);\n\t\t}\n\t}\n\n\t/**\n\t * @returns {void} */\n\ti(anchor) {\n\t\tfor (let i = 0; i < this.n.length; i += 1) {\n\t\t\tinsert_hydration(this.t, this.n[i], anchor);\n\t\t}\n\t}\n}\n\n/**\n * @param {NamedNodeMap} attributes\n * @returns {{}}\n */\nexport function attribute_to_object(attributes) {\n\tconst result = {};\n\tfor (const attribute of attributes) {\n\t\tresult[attribute.name] = attribute.value;\n\t}\n\treturn result;\n}\n\nconst escaped = {\n\t'\"': '&quot;',\n\t'&': '&amp;',\n\t'<': '&lt;'\n};\n\nconst regex_attribute_characters_to_escape = /[\"&<]/g;\n\n/**\n * Note that the attribute itself should be surrounded in double quotes\n * @param {any} attribute\n */\nfunction escape_attribute(attribute) {\n\treturn String(attribute).replace(regex_attribute_characters_to_escape, (match) => escaped[match]);\n}\n\n/**\n * @param {Record<string, string>} attributes\n */\nexport function stringify_spread(attributes) {\n\tlet str = ' ';\n\tfor (const key in attributes) {\n\t\tif (attributes[key] != null) {\n\t\t\tstr += `${key}=\"${escape_attribute(attributes[key])}\" `;\n\t\t}\n\t}\n\n\treturn str;\n}\n\n/**\n * @param {HTMLElement} element\n * @returns {{}}\n */\nexport function get_custom_elements_slots(element) {\n\tconst result = {};\n\telement.childNodes.forEach(\n\t\t/** @param {Element} node */ (node) => {\n\t\t\tresult[node.slot || 'default'] = true;\n\t\t}\n\t);\n\treturn result;\n}\n\nexport function construct_svelte_component(component, props) {\n\treturn new component(props);\n}\n\n/**\n * @typedef {Node & {\n * \tclaim_order?: number;\n * \thydrate_init?: true;\n * \tactual_end_child?: NodeEx;\n * \tchildNodes: NodeListOf<NodeEx>;\n * }} NodeEx\n */\n\n/** @typedef {ChildNode & NodeEx} ChildNodeEx */\n\n/** @typedef {NodeEx & { claim_order: number }} NodeEx2 */\n\n/**\n * @typedef {ChildNodeEx[] & {\n * \tclaim_info?: {\n * \t\tlast_index: number;\n * \t\ttotal_claimed: number;\n * \t};\n * }} ChildNodeArray\n */\n", "import { custom_event } from './dom.js';\n\nexport let current_component;\n\n/** @returns {void} */\nexport function set_current_component(component) {\n\tcurrent_component = component;\n}\n\nexport function get_current_component() {\n\tif (!current_component) throw new Error('Function called outside component initialization');\n\treturn current_component;\n}\n\n/**\n * Schedules a callback to run immediately before the component is updated after any state change.\n *\n * The first time the callback runs will be before the initial `onMount`\n *\n * https://svelte.dev/docs/svelte#beforeupdate\n * @param {() => any} fn\n * @returns {void}\n */\nexport function beforeUpdate(fn) {\n\tget_current_component().$$.before_update.push(fn);\n}\n\n/**\n * The `onMount` function schedules a callback to run as soon as the component has been mounted to the DOM.\n * It must be called during the component's initialisation (but doesn't need to live *inside* the component;\n * it can be called from an external module).\n *\n * If a function is returned _synchronously_ from `onMount`, it will be called when the component is unmounted.\n *\n * `onMount` does not run inside a [server-side component](https://svelte.dev/docs#run-time-server-side-component-api).\n *\n * https://svelte.dev/docs/svelte#onmount\n * @template T\n * @param {() => import('./private.js').NotFunction<T> | Promise<import('./private.js').NotFunction<T>> | (() => any)} fn\n * @returns {void}\n */\nexport function onMount(fn) {\n\tget_current_component().$$.on_mount.push(fn);\n}\n\n/**\n * Schedules a callback to run immediately after the component has been updated.\n *\n * The first time the callback runs will be after the initial `onMount`\n *\n * https://svelte.dev/docs/svelte#afterupdate\n * @param {() => any} fn\n * @returns {void}\n */\nexport function afterUpdate(fn) {\n\tget_current_component().$$.after_update.push(fn);\n}\n\n/**\n * Schedules a callback to run immediately before the component is unmounted.\n *\n * Out of `onMount`, `beforeUpdate`, `afterUpdate` and `onDestroy`, this is the\n * only one that runs inside a server-side component.\n *\n * https://svelte.dev/docs/svelte#ondestroy\n * @param {() => any} fn\n * @returns {void}\n */\nexport function onDestroy(fn) {\n\tget_current_component().$$.on_destroy.push(fn);\n}\n\n/**\n * Creates an event dispatcher that can be used to dispatch [component events](https://svelte.dev/docs#template-syntax-component-directives-on-eventname).\n * Event dispatchers are functions that can take two arguments: `name` and `detail`.\n *\n * Component events created with `createEventDispatcher` create a\n * [CustomEvent](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent).\n * These events do not [bubble](https://developer.mozilla.org/en-US/docs/Learn/JavaScript/Building_blocks/Events#Event_bubbling_and_capture).\n * The `detail` argument corresponds to the [CustomEvent.detail](https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/detail)\n * property and can contain any type of data.\n *\n * The event dispatcher can be typed to narrow the allowed event names and the type of the `detail` argument:\n * ```ts\n * const dispatch = createEventDispatcher<{\n *  loaded: never; // does not take a detail argument\n *  change: string; // takes a detail argument of type string, which is required\n *  optional: number | null; // takes an optional detail argument of type number\n * }>();\n * ```\n *\n * https://svelte.dev/docs/svelte#createeventdispatcher\n * @template {Record<string, any>} [EventMap=any]\n * @returns {import('./public.js').EventDispatcher<EventMap>}\n */\nexport function createEventDispatcher() {\n\tconst component = get_current_component();\n\treturn (type, detail, { cancelable = false } = {}) => {\n\t\tconst callbacks = component.$$.callbacks[type];\n\t\tif (callbacks) {\n\t\t\t// TODO are there situations where events could be dispatched\n\t\t\t// in a server (non-DOM) environment?\n\t\t\tconst event = custom_event(/** @type {string} */ (type), detail, { cancelable });\n\t\t\tcallbacks.slice().forEach((fn) => {\n\t\t\t\tfn.call(component, event);\n\t\t\t});\n\t\t\treturn !event.defaultPrevented;\n\t\t}\n\t\treturn true;\n\t};\n}\n\n/**\n * Associates an arbitrary `context` object with the current component and the specified `key`\n * and returns that object. The context is then available to children of the component\n * (including slotted content) with `getContext`.\n *\n * Like lifecycle functions, this must be called during component initialisation.\n *\n * https://svelte.dev/docs/svelte#setcontext\n * @template T\n * @param {any} key\n * @param {T} context\n * @returns {T}\n */\nexport function setContext(key, context) {\n\tget_current_component().$$.context.set(key, context);\n\treturn context;\n}\n\n/**\n * Retrieves the context that belongs to the closest parent component with the specified `key`.\n * Must be called during component initialisation.\n *\n * https://svelte.dev/docs/svelte#getcontext\n * @template T\n * @param {any} key\n * @returns {T}\n */\nexport function getContext(key) {\n\treturn get_current_component().$$.context.get(key);\n}\n\n/**\n * Retrieves the whole context map that belongs to the closest parent component.\n * Must be called during component initialisation. Useful, for example, if you\n * programmatically create a component and want to pass the existing context to it.\n *\n * https://svelte.dev/docs/svelte#getallcontexts\n * @template {Map<any, any>} [T=Map<any, any>]\n * @returns {T}\n */\nexport function getAllContexts() {\n\treturn get_current_component().$$.context;\n}\n\n/**\n * Checks whether a given `key` has been set in the context of a parent component.\n * Must be called during component initialisation.\n *\n * https://svelte.dev/docs/svelte#hascontext\n * @param {any} key\n * @returns {boolean}\n */\nexport function hasContext(key) {\n\treturn get_current_component().$$.context.has(key);\n}\n\n// TODO figure out if we still want to support\n// shorthand events, or if we want to implement\n// a real bubbling mechanism\n/**\n * @param component\n * @param event\n * @returns {void}\n */\nexport function bubble(component, event) {\n\tconst callbacks = component.$$.callbacks[event.type];\n\tif (callbacks) {\n\t\t// @ts-ignore\n\t\tcallbacks.slice().forEach((fn) => fn.call(this, event));\n\t}\n}\n", "import { transition_in, transition_out } from './transitions.js';\nimport { run_all } from './utils.js';\n\n// general each functions:\n\nexport function ensure_array_like(array_like_or_iterator) {\n\treturn array_like_or_iterator?.length !== undefined\n\t\t? array_like_or_iterator\n\t\t: Array.from(array_like_or_iterator);\n}\n\n// keyed each functions:\n\n/** @returns {void} */\nexport function destroy_block(block, lookup) {\n\tblock.d(1);\n\tlookup.delete(block.key);\n}\n\n/** @returns {void} */\nexport function outro_and_destroy_block(block, lookup) {\n\ttransition_out(block, 1, 1, () => {\n\t\tlookup.delete(block.key);\n\t});\n}\n\n/** @returns {void} */\nexport function fix_and_destroy_block(block, lookup) {\n\tblock.f();\n\tdestroy_block(block, lookup);\n}\n\n/** @returns {void} */\nexport function fix_and_outro_and_destroy_block(block, lookup) {\n\tblock.f();\n\toutro_and_destroy_block(block, lookup);\n}\n\n/** @returns {any[]} */\nexport function update_keyed_each(\n\told_blocks,\n\tdirty,\n\tget_key,\n\tdynamic,\n\tctx,\n\tlist,\n\tlookup,\n\tnode,\n\tdestroy,\n\tcreate_each_block,\n\tnext,\n\tget_context\n) {\n\tlet o = old_blocks.length;\n\tlet n = list.length;\n\tlet i = o;\n\tconst old_indexes = {};\n\twhile (i--) old_indexes[old_blocks[i].key] = i;\n\tconst new_blocks = [];\n\tconst new_lookup = new Map();\n\tconst deltas = new Map();\n\tconst updates = [];\n\ti = n;\n\twhile (i--) {\n\t\tconst child_ctx = get_context(ctx, list, i);\n\t\tconst key = get_key(child_ctx);\n\t\tlet block = lookup.get(key);\n\t\tif (!block) {\n\t\t\tblock = create_each_block(key, child_ctx);\n\t\t\tblock.c();\n\t\t} else if (dynamic) {\n\t\t\t// defer updates until all the DOM shuffling is done\n\t\t\tupdates.push(() => block.p(child_ctx, dirty));\n\t\t}\n\t\tnew_lookup.set(key, (new_blocks[i] = block));\n\t\tif (key in old_indexes) deltas.set(key, Math.abs(i - old_indexes[key]));\n\t}\n\tconst will_move = new Set();\n\tconst did_move = new Set();\n\t/** @returns {void} */\n\tfunction insert(block) {\n\t\ttransition_in(block, 1);\n\t\tblock.m(node, next);\n\t\tlookup.set(block.key, block);\n\t\tnext = block.first;\n\t\tn--;\n\t}\n\twhile (o && n) {\n\t\tconst new_block = new_blocks[n - 1];\n\t\tconst old_block = old_blocks[o - 1];\n\t\tconst new_key = new_block.key;\n\t\tconst old_key = old_block.key;\n\t\tif (new_block === old_block) {\n\t\t\t// do nothing\n\t\t\tnext = new_block.first;\n\t\t\to--;\n\t\t\tn--;\n\t\t} else if (!new_lookup.has(old_key)) {\n\t\t\t// remove old block\n\t\t\tdestroy(old_block, lookup);\n\t\t\to--;\n\t\t} else if (!lookup.has(new_key) || will_move.has(new_key)) {\n\t\t\tinsert(new_block);\n\t\t} else if (did_move.has(old_key)) {\n\t\t\to--;\n\t\t} else if (deltas.get(new_key) > deltas.get(old_key)) {\n\t\t\tdid_move.add(new_key);\n\t\t\tinsert(new_block);\n\t\t} else {\n\t\t\twill_move.add(old_key);\n\t\t\to--;\n\t\t}\n\t}\n\twhile (o--) {\n\t\tconst old_block = old_blocks[o];\n\t\tif (!new_lookup.has(old_block.key)) destroy(old_block, lookup);\n\t}\n\twhile (n) insert(new_blocks[n - 1]);\n\trun_all(updates);\n\treturn new_blocks;\n}\n\n/** @returns {void} */\nexport function validate_each_keys(ctx, list, get_context, get_key) {\n\tconst keys = new Map();\n\tfor (let i = 0; i < list.length; i++) {\n\t\tconst key = get_key(get_context(ctx, list, i));\n\t\tif (keys.has(key)) {\n\t\t\tlet value = '';\n\t\t\ttry {\n\t\t\t\tvalue = `with value '${String(key)}' `;\n\t\t\t} catch (e) {\n\t\t\t\t// can't stringify\n\t\t\t}\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot have duplicate keys in a keyed each: Keys at index ${keys.get(\n\t\t\t\t\tkey\n\t\t\t\t)} and ${i} ${value}are duplicates`\n\t\t\t);\n\t\t}\n\t\tkeys.set(key, i);\n\t}\n}\n", "const _boolean_attributes = /** @type {const} */ ([\n\t'allowfullscreen',\n\t'allowpaymentrequest',\n\t'async',\n\t'autofocus',\n\t'autoplay',\n\t'checked',\n\t'controls',\n\t'default',\n\t'defer',\n\t'disabled',\n\t'formnovalidate',\n\t'hidden',\n\t'inert',\n\t'ismap',\n\t'loop',\n\t'multiple',\n\t'muted',\n\t'nomodule',\n\t'novalidate',\n\t'open',\n\t'playsinline',\n\t'readonly',\n\t'required',\n\t'reversed',\n\t'selected'\n]);\n\n/**\n * List of HTML boolean attributes (e.g. `<input disabled>`).\n * Source: https://html.spec.whatwg.org/multipage/indices.html\n *\n * @type {Set<string>}\n */\nexport const boolean_attributes = new Set([..._boolean_attributes]);\n\n/** @typedef {typeof _boolean_attributes[number]} BooleanAttributes */\n", "import { set_current_component, current_component } from './lifecycle.js';\nimport { run_all, blank_object } from './utils.js';\nimport { boolean_attributes } from '../../shared/boolean_attributes.js';\nimport { ensure_array_like } from './each.js';\nexport { is_void } from '../../shared/utils/names.js';\n\nexport const invalid_attribute_name_character =\n\t/[\\s'\">/=\\u{FDD0}-\\u{FDEF}\\u{FFFE}\\u{FFFF}\\u{1FFFE}\\u{1FFFF}\\u{2FFFE}\\u{2FFFF}\\u{3FFFE}\\u{3FFFF}\\u{4FFFE}\\u{4FFFF}\\u{5FFFE}\\u{5FFFF}\\u{6FFFE}\\u{6FFFF}\\u{7FFFE}\\u{7FFFF}\\u{8FFFE}\\u{8FFFF}\\u{9FFFE}\\u{9FFFF}\\u{AFFFE}\\u{AFFFF}\\u{BFFFE}\\u{BFFFF}\\u{CFFFE}\\u{CFFFF}\\u{DFFFE}\\u{DFFFF}\\u{EFFFE}\\u{EFFFF}\\u{FFFFE}\\u{FFFFF}\\u{10FFFE}\\u{10FFFF}]/u;\n// https://html.spec.whatwg.org/multipage/syntax.html#attributes-2\n// https://infra.spec.whatwg.org/#noncharacter\n\n/** @returns {string} */\nexport function spread(args, attrs_to_add) {\n\tconst attributes = Object.assign({}, ...args);\n\tif (attrs_to_add) {\n\t\tconst classes_to_add = attrs_to_add.classes;\n\t\tconst styles_to_add = attrs_to_add.styles;\n\t\tif (classes_to_add) {\n\t\t\tif (attributes.class == null) {\n\t\t\t\tattributes.class = classes_to_add;\n\t\t\t} else {\n\t\t\t\tattributes.class += ' ' + classes_to_add;\n\t\t\t}\n\t\t}\n\t\tif (styles_to_add) {\n\t\t\tif (attributes.style == null) {\n\t\t\t\tattributes.style = style_object_to_string(styles_to_add);\n\t\t\t} else {\n\t\t\t\tattributes.style = style_object_to_string(\n\t\t\t\t\tmerge_ssr_styles(attributes.style, styles_to_add)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\tlet str = '';\n\tObject.keys(attributes).forEach((name) => {\n\t\tif (invalid_attribute_name_character.test(name)) return;\n\t\tconst value = attributes[name];\n\t\tif (value === true) str += ' ' + name;\n\t\telse if (boolean_attributes.has(name.toLowerCase())) {\n\t\t\tif (value) str += ' ' + name;\n\t\t} else if (value != null) {\n\t\t\tstr += ` ${name}=\"${value}\"`;\n\t\t}\n\t});\n\treturn str;\n}\n\n/** @returns {{}} */\nexport function merge_ssr_styles(style_attribute, style_directive) {\n\tconst style_object = {};\n\tfor (const individual_style of style_attribute.split(';')) {\n\t\tconst colon_index = individual_style.indexOf(':');\n\t\tconst name = individual_style.slice(0, colon_index).trim();\n\t\tconst value = individual_style.slice(colon_index + 1).trim();\n\t\tif (!name) continue;\n\t\tstyle_object[name] = value;\n\t}\n\tfor (const name in style_directive) {\n\t\tconst value = style_directive[name];\n\t\tif (value) {\n\t\t\tstyle_object[name] = value;\n\t\t} else {\n\t\t\tdelete style_object[name];\n\t\t}\n\t}\n\treturn style_object;\n}\n\nconst ATTR_REGEX = /[&\"]/g;\nconst CONTENT_REGEX = /[&<]/g;\n\n/**\n * Note: this method is performance sensitive and has been optimized\n * https://github.com/sveltejs/svelte/pull/5701\n * @param {unknown} value\n * @returns {string}\n */\nexport function escape(value, is_attr = false) {\n\tconst str = String(value);\n\tconst pattern = is_attr ? ATTR_REGEX : CONTENT_REGEX;\n\tpattern.lastIndex = 0;\n\tlet escaped = '';\n\tlet last = 0;\n\twhile (pattern.test(str)) {\n\t\tconst i = pattern.lastIndex - 1;\n\t\tconst ch = str[i];\n\t\tescaped += str.substring(last, i) + (ch === '&' ? '&amp;' : ch === '\"' ? '&quot;' : '&lt;');\n\t\tlast = i + 1;\n\t}\n\treturn escaped + str.substring(last);\n}\n\nexport function escape_attribute_value(value) {\n\t// keep booleans, null, and undefined for the sake of `spread`\n\tconst should_escape = typeof value === 'string' || (value && typeof value === 'object');\n\treturn should_escape ? escape(value, true) : value;\n}\n\n/** @returns {{}} */\nexport function escape_object(obj) {\n\tconst result = {};\n\tfor (const key in obj) {\n\t\tresult[key] = escape_attribute_value(obj[key]);\n\t}\n\treturn result;\n}\n\n/** @returns {string} */\nexport function each(items, fn) {\n\titems = ensure_array_like(items);\n\tlet str = '';\n\tfor (let i = 0; i < items.length; i += 1) {\n\t\tstr += fn(items[i], i);\n\t}\n\treturn str;\n}\n\nexport const missing_component = {\n\t$$render: () => ''\n};\n\nexport function validate_component(component, name) {\n\tif (!component || !component.$$render) {\n\t\tif (name === 'svelte:component') name += ' this={...}';\n\t\tthrow new Error(\n\t\t\t`<${name}> is not a valid SSR component. You may need to review your build config to ensure that dependencies are compiled, rather than imported as pre-compiled modules. Otherwise you may need to fix a <${name}>.`\n\t\t);\n\t}\n\treturn component;\n}\n\n/** @returns {string} */\nexport function debug(file, line, column, values) {\n\tconsole.log(`{@debug} ${file ? file + ' ' : ''}(${line}:${column})`); // eslint-disable-line no-console\n\tconsole.log(values); // eslint-disable-line no-console\n\treturn '';\n}\n\nlet on_destroy;\n\n/** @returns {{ render: (props?: {}, { $$slots, context }?: { $$slots?: {}; context?: Map<any, any>; }) => { html: any; css: { code: string; map: any; }; head: string; }; $$render: (result: any, props: any, bindings: any, slots: any, context: any) => any; }} */\nexport function create_ssr_component(fn) {\n\tfunction $$render(result, props, bindings, slots, context) {\n\t\tconst parent_component = current_component;\n\t\tconst $$ = {\n\t\t\ton_destroy,\n\t\t\tcontext: new Map(context || (parent_component ? parent_component.$$.context : [])),\n\t\t\t// these will be immediately discarded\n\t\t\ton_mount: [],\n\t\t\tbefore_update: [],\n\t\t\tafter_update: [],\n\t\t\tcallbacks: blank_object()\n\t\t};\n\t\tset_current_component({ $$ });\n\t\tconst html = fn(result, props, bindings, slots);\n\t\tset_current_component(parent_component);\n\t\treturn html;\n\t}\n\treturn {\n\t\trender: (props = {}, { $$slots = {}, context = new Map() } = {}) => {\n\t\t\ton_destroy = [];\n\t\t\tconst result = { title: '', head: '', css: new Set() };\n\t\t\tconst html = $$render(result, props, {}, $$slots, context);\n\t\t\trun_all(on_destroy);\n\t\t\treturn {\n\t\t\t\thtml,\n\t\t\t\tcss: {\n\t\t\t\t\tcode: Array.from(result.css)\n\t\t\t\t\t\t.map((css) => css.code)\n\t\t\t\t\t\t.join('\\n'),\n\t\t\t\t\tmap: null // TODO\n\t\t\t\t},\n\t\t\t\thead: result.title + result.head\n\t\t\t};\n\t\t},\n\t\t$$render\n\t};\n}\n\n/** @returns {string} */\nexport function add_attribute(name, value, boolean) {\n\tif (value == null || (boolean && !value)) return '';\n\tconst assignment = boolean && value === true ? '' : `=\"${escape(value, true)}\"`;\n\treturn ` ${name}${assignment}`;\n}\n\n/** @returns {string} */\nexport function add_classes(classes) {\n\treturn classes ? ` class=\"${classes}\"` : '';\n}\n\n/** @returns {string} */\nfunction style_object_to_string(style_object) {\n\treturn Object.keys(style_object)\n\t\t.filter((key) => style_object[key])\n\t\t.map((key) => `${key}: ${escape_attribute_value(style_object[key])};`)\n\t\t.join(' ');\n}\n\n/** @returns {string} */\nexport function add_styles(style_object) {\n\tconst styles = style_object_to_string(style_object);\n\treturn styles ? ` style=\"${styles}\"` : '';\n}\n"], "names": [], "mappings": "AAAA;AACO,SAAS,IAAI,GAAG,EAAE;AACzB;AACY,MAAC,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE;AACjC;AACA,CAAC,KAAK,MAAM,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACtC,CAAC,6BAA6B,GAAG,EAAE;AACnC,CAAC;AAsBD;AACO,SAAS,GAAG,CAAC,EAAE,EAAE;AACxB,CAAC,OAAO,EAAE,EAAE,CAAC;AACb,CAAC;AACD;AACO,SAAS,YAAY,GAAG;AAC/B,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,GAAG,EAAE;AAC7B,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAClB,CAAC;AACD;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW,CAAC,KAAK,EAAE;AACnC,CAAC,OAAO,OAAO,KAAK,KAAK,UAAU,CAAC;AACpC,CAAC;AACD;AACA;AACO,SAAS,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE;AACrC,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC;AAC7F,CAAC;AAoDD;AACA;AACO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAC9B,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AACtC,CAAC;AAQD;AACO,SAAS,SAAS,CAAC,KAAK,EAAE,GAAG,SAAS,EAAE;AAC/C,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE;AACpB,EAAE,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AACpC,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AACvB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF,CAAC,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AAC7C,CAAC,OAAO,KAAK,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC;AAC9D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,CAAC,KAAK,EAAE;AACvC,CAAC,IAAI,KAAK,CAAC;AACX,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AACxC,CAAC,OAAO,KAAK,CAAC;AACd,CAAC;AAqFD;AACA;AACO,SAAS,kBAAkB,CAAC,KAAK,EAAE,IAAI,EAAE;AAChD,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;AACjB,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;AACtB,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7E,CAAC,OAAO,IAAI,CAAC;AACb,CAAC;AAoBD;AACO,SAAS,aAAa,CAAC,KAAK,EAAE;AACrC,CAAC,OAAO,KAAK,IAAI,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;AACnC,CAAC;AACD;AACO,SAAS,eAAe,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACnD,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAClB,CAAC,OAAO,GAAG,CAAC;AACZ;;AC4tBA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,GAAG,KAAK,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AACzF,CAAC,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC,CAAC;AAC/D,CAAC;AAsND;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AChuCU,IAAC,kBAAkB;AAC7B;AACA;AACO,SAAS,qBAAqB,CAAC,SAAS,EAAE;AACjD,CAAC,iBAAiB,GAAG,SAAS,CAAC;AAC/B,CAAC;AACD;AACO,SAAS,qBAAqB,GAAG;AACxC,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;AAC7F,CAAC,OAAO,iBAAiB,CAAC;AAC1B,CAAC;AA6CD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,SAAS,CAAC,EAAE,EAAE;AAC9B,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAChD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,qBAAqB,GAAG;AACxC,CAAC,MAAM,SAAS,GAAG,qBAAqB,EAAE,CAAC;AAC3C,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,EAAE,KAAK;AACvD,EAAE,MAAM,SAAS,GAAG,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACjD,EAAE,IAAI,SAAS,EAAE;AACjB;AACA;AACA,GAAG,MAAM,KAAK,GAAG,YAAY,wBAAwB,IAAI,GAAG,MAAM,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;AACpF,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK;AACrC,IAAI,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAC9B,IAAI,CAAC,CAAC;AACN,GAAG,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,EAAE,CAAC;AACH,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,GAAG,EAAE,OAAO,EAAE;AACzC,CAAC,qBAAqB,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACtD,CAAC,OAAO,OAAO,CAAC;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,GAAG,EAAE;AAChC,CAAC,OAAO,qBAAqB,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpD,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,cAAc,GAAG;AACjC,CAAC,OAAO,qBAAqB,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC;AAC3C,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,UAAU,CAAC,GAAG,EAAE;AAChC,CAAC,OAAO,qBAAqB,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpD;;ACnKA;AACA;AACO,SAAS,iBAAiB,CAAC,sBAAsB,EAAE;AAC1D,CAAC,OAAO,sBAAsB,EAAE,MAAM,KAAK,SAAS;AACpD,IAAI,sBAAsB;AAC1B,IAAI,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACvC;;ACTA,MAAM,mBAAmB,yBAAyB;AAClD,CAAC,iBAAiB;AAClB,CAAC,qBAAqB;AACtB,CAAC,OAAO;AACR,CAAC,WAAW;AACZ,CAAC,UAAU;AACX,CAAC,SAAS;AACV,CAAC,UAAU;AACX,CAAC,SAAS;AACV,CAAC,OAAO;AACR,CAAC,UAAU;AACX,CAAC,gBAAgB;AACjB,CAAC,QAAQ;AACT,CAAC,OAAO;AACR,CAAC,OAAO;AACR,CAAC,MAAM;AACP,CAAC,UAAU;AACX,CAAC,OAAO;AACR,CAAC,UAAU;AACX,CAAC,YAAY;AACb,CAAC,MAAM;AACP,CAAC,aAAa;AACd,CAAC,UAAU;AACX,CAAC,UAAU;AACX,CAAC,UAAU;AACX,CAAC,UAAU;AACX,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,mBAAmB,CAAC,CAAC,CAAC;AACpE;AACA;;AC9BO,MAAM,gCAAgC;AAC7C,CAAC,+UAA+U,CAAC;AACjV;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,IAAI,EAAE,YAAY,EAAE;AAC3C,CAAC,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAC;AAC/C,CAAC,IAAI,YAAY,EAAE;AACnB,EAAE,MAAM,cAAc,GAAG,YAAY,CAAC,OAAO,CAAC;AAC9C,EAAE,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC;AAC5C,EAAE,IAAI,cAAc,EAAE;AACtB,GAAG,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,EAAE;AACjC,IAAI,UAAU,CAAC,KAAK,GAAG,cAAc,CAAC;AACtC,IAAI,MAAM;AACV,IAAI,UAAU,CAAC,KAAK,IAAI,GAAG,GAAG,cAAc,CAAC;AAC7C,IAAI;AACJ,GAAG;AACH,EAAE,IAAI,aAAa,EAAE;AACrB,GAAG,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,EAAE;AACjC,IAAI,UAAU,CAAC,KAAK,GAAG,sBAAsB,CAAC,aAAa,CAAC,CAAC;AAC7D,IAAI,MAAM;AACV,IAAI,UAAU,CAAC,KAAK,GAAG,sBAAsB;AAC7C,KAAK,gBAAgB,CAAC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC;AACtD,KAAK,CAAC;AACN,IAAI;AACJ,GAAG;AACH,EAAE;AACF,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;AACd,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC3C,EAAE,IAAI,gCAAgC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO;AAC1D,EAAE,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACjC,EAAE,IAAI,KAAK,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC;AACxC,OAAO,IAAI,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE;AACvD,GAAG,IAAI,KAAK,EAAE,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC;AAChC,GAAG,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;AAC5B,GAAG,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;AAChC,GAAG;AACH,EAAE,CAAC,CAAC;AACJ,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC;AACD;AACA;AACO,SAAS,gBAAgB,CAAC,eAAe,EAAE,eAAe,EAAE;AACnE,CAAC,MAAM,YAAY,GAAG,EAAE,CAAC;AACzB,CAAC,KAAK,MAAM,gBAAgB,IAAI,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC5D,EAAE,MAAM,WAAW,GAAG,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACpD,EAAE,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC;AAC7D,EAAE,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC/D,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS;AACtB,EAAE,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAC7B,EAAE;AACF,CAAC,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE;AACrC,EAAE,MAAM,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;AACtC,EAAE,IAAI,KAAK,EAAE;AACb,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAC9B,GAAG,MAAM;AACT,GAAG,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE;AACF,CAAC,OAAO,YAAY,CAAC;AACrB,CAAC;AACD;AACA,MAAM,UAAU,GAAG,OAAO,CAAC;AAC3B,MAAM,aAAa,GAAG,OAAO,CAAC;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,MAAM,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE;AAC/C,CAAC,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3B,CAAC,MAAM,OAAO,GAAG,OAAO,GAAG,UAAU,GAAG,aAAa,CAAC;AACtD,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;AACvB,CAAC,IAAI,OAAO,GAAG,EAAE,CAAC;AAClB,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC;AACd,CAAC,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AAC3B,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;AAClC,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,OAAO,IAAI,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,OAAO,GAAG,EAAE,KAAK,GAAG,GAAG,QAAQ,GAAG,MAAM,CAAC,CAAC;AAC9F,EAAE,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;AACf,EAAE;AACF,CAAC,OAAO,OAAO,GAAG,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC;AACD;AACO,SAAS,sBAAsB,CAAC,KAAK,EAAE;AAC9C;AACA,CAAC,MAAM,aAAa,GAAG,OAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC;AACzF,CAAC,OAAO,aAAa,GAAG,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC;AACpD,CAAC;AACD;AACA;AACO,SAAS,aAAa,CAAC,GAAG,EAAE;AACnC,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC;AACnB,CAAC,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;AACxB,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,sBAAsB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjD,EAAE;AACF,CAAC,OAAO,MAAM,CAAC;AACf,CAAC;AACD;AACA;AACO,SAAS,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE;AAChC,CAAC,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAClC,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;AACd,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAC3C,EAAE,GAAG,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB,EAAE;AACF,CAAC,OAAO,GAAG,CAAC;AACZ,CAAC;AACD;AACY,MAAC,iBAAiB,GAAG;AACjC,CAAC,QAAQ,EAAE,MAAM,EAAE;AACnB,EAAE;AACF;AACO,SAAS,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE;AACpD,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AACxC,EAAE,IAAI,IAAI,KAAK,kBAAkB,EAAE,IAAI,IAAI,aAAa,CAAC;AACzD,EAAE,MAAM,IAAI,KAAK;AACjB,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,kMAAkM,EAAE,IAAI,CAAC,EAAE,CAAC;AACxN,GAAG,CAAC;AACJ,EAAE;AACF,CAAC,OAAO,SAAS,CAAC;AAClB,CAAC;AAQD;AACA,IAAI,UAAU,CAAC;AACf;AACA;AACO,SAAS,oBAAoB,CAAC,EAAE,EAAE;AACzC,CAAC,SAAS,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;AAC5D,EAAE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC;AAC7C,EAAE,MAAM,EAAE,GAAG;AACb,GAAG,UAAU;AACb,GAAG,OAAO,EAAE,IAAI,GAAG,CAAC,OAAO,KAAK,gBAAgB,GAAG,gBAAgB,CAAC,EAAE,CAAC,OAAO,GAAG,EAAE,CAAC,CAAC;AACrF;AACA,GAAG,QAAQ,EAAE,EAAE;AACf,GAAG,aAAa,EAAE,EAAE;AACpB,GAAG,YAAY,EAAE,EAAE;AACnB,GAAG,SAAS,EAAE,YAAY,EAAE;AAC5B,GAAG,CAAC;AACJ,EAAE,qBAAqB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAChC,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAClD,EAAE,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;AAC1C,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF,CAAC,OAAO;AACR,EAAE,MAAM,EAAE,CAAC,KAAK,GAAG,EAAE,EAAE,EAAE,OAAO,GAAG,EAAE,EAAE,OAAO,GAAG,IAAI,GAAG,EAAE,EAAE,GAAG,EAAE,KAAK;AACtE,GAAG,UAAU,GAAG,EAAE,CAAC;AACnB,GAAG,MAAM,MAAM,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC;AAC1D,GAAG,MAAM,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC9D,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AACvB,GAAG,OAAO;AACV,IAAI,IAAI;AACR,IAAI,GAAG,EAAE;AACT,KAAK,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;AACjC,OAAO,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC;AAC7B,OAAO,IAAI,CAAC,IAAI,CAAC;AACjB,KAAK,GAAG,EAAE,IAAI;AACd,KAAK;AACL,IAAI,IAAI,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI;AACpC,IAAI,CAAC;AACL,GAAG;AACH,EAAE,QAAQ;AACV,EAAE,CAAC;AACH,CAAC;AACD;AACA;AACO,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;AACpD,CAAC,IAAI,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC;AACrD,CAAC,MAAM,UAAU,GAAG,OAAO,IAAI,KAAK,KAAK,IAAI,GAAG,EAAE,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjF,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;AAChC,CAAC;AACD;AACA;AACO,SAAS,WAAW,CAAC,OAAO,EAAE;AACrC,CAAC,OAAO,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC7C,CAAC;AACD;AACA;AACA,SAAS,sBAAsB,CAAC,YAAY,EAAE;AAC9C,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;AACjC,GAAG,MAAM,CAAC,CAAC,GAAG,KAAK,YAAY,CAAC,GAAG,CAAC,CAAC;AACrC,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,EAAE,sBAAsB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACb,CAAC;AACD;AACA;AACO,SAAS,UAAU,CAAC,YAAY,EAAE;AACzC,CAAC,MAAM,MAAM,GAAG,sBAAsB,CAAC,YAAY,CAAC,CAAC;AACrD,CAAC,OAAO,MAAM,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC3C;;;;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5]}