const index = 1;
let component_cache;
const component = async () => component_cache ??= (await import('./error.svelte-CJ3DTY-J.js')).default;
const imports = ["_app/immutable/nodes/1.CMRU4NuT.js","_app/immutable/chunks/stores.vbHk8Adl.js","_app/immutable/chunks/client.B96STMEW.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, stylesheets };
//# sourceMappingURL=1-BFPimMmO.js.map
