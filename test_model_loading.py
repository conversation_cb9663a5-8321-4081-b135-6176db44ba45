#!/usr/bin/env python3
"""
Test script to debug BAGEL model loading
"""

import os
import torch
from accelerate import load_checkpoint_and_dispatch
from modeling.bagel import BagelConfig, <PERSON><PERSON>

def test_model_loading():
    print("🧪 BAGEL Model Loading Test")
    print("=" * 50)

    # Check CUDA
    print(f"🎮 CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"📊 GPU: {torch.cuda.get_device_name(0)}")
        print(f"💾 GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

    # Model paths
    model_path = "models/BAGEL-7B-MoT"
    llm_config_path = os.path.join(model_path, "llm_config.json")
    vit_config_path = os.path.join(model_path, "vit_config.json")
    checkpoint_path = os.path.join(model_path, "ema.safetensors")

    # Check files exist
    print("\n📁 Checking model files...")
    for path, name in [(llm_config_path, "LLM config"),
                       (vit_config_path, "VIT config"),
                       (checkpoint_path, "Model checkpoint")]:
        if os.path.exists(path):
            size = os.path.getsize(path) / 1024**3
            print(f"✅ {name}: {size:.2f} GB")
        else:
            print(f"❌ {name}: Missing")
            return False

    try:
        print("\n🔧 Loading model configuration...")
        config = BagelConfig.from_pretrained(model_path)
        print(f"✅ Config loaded successfully")

        print("\n🏗️  Initializing model...")
        model = Bagel(config)
        print(f"✅ Model initialized")

        print(f"📊 Model parameters: {sum(p.numel() for p in model.parameters()) / 1e9:.1f}B")

        print("\n⚡ Loading checkpoint (this may take a while)...")

        # Try loading with different strategies
        device_map = "auto" if torch.cuda.is_available() else "cpu"
        print(f"🎯 Device map: {device_map}")

        model = load_checkpoint_and_dispatch(
            model,
            checkpoint=checkpoint_path,
            device_map=device_map,
            offload_buffers=True,
            offload_folder="offload",
            dtype=torch.bfloat16,
            force_hooks=True,
        ).eval()

        print("🎉 Model loaded successfully!")

        if torch.cuda.is_available():
            print(f"💾 GPU memory used: {torch.cuda.memory_allocated(0) / 1024**3:.1f} GB")

        return True

    except Exception as e:
        print(f"❌ Error loading model: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_loading()
    if success:
        print("\n✅ Model loading test passed!")
    else:
        print("\n❌ Model loading test failed!")
