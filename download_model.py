#!/usr/bin/env python3
"""
Official BAGEL model download script following the exact instructions
"""

from huggingface_hub import snapshot_download
import os

# Following the official instructions exactly
save_dir = "models/BAGEL-7B-MoT"  # Path to save BAGEL-7B-MoT
repo_id = "ByteDance-Seed/BAGEL-7B-MoT"
cache_dir = save_dir + "/cache"

print("🥯 BAGEL Model Download")
print("=" * 50)
print(f"📥 Downloading from: {repo_id}")
print(f"💾 Saving to: {save_dir}")
print(f"🗂️  Cache directory: {cache_dir}")
print("\n⏳ This may take a while (downloading ~14GB)...")
print("💡 You can interrupt and resume the download later")

# Create directory if it doesn't exist
os.makedirs(os.path.dirname(save_dir), exist_ok=True)

try:
    snapshot_download(
        cache_dir=cache_dir,
        local_dir=save_dir,
        repo_id=repo_id,
        local_dir_use_symlinks=False,
        resume_download=True,
        allow_patterns=["*.json", "*.safetensors", "*.bin", "*.py", "*.md", "*.txt"],
    )

    print("\n🎉 Download completed successfully!")
    print("\n📋 Next steps:")
    print("1. Run: python app.py")
    print("2. Open browser to: http://127.0.0.1:7860")
    print("3. Start using BAGEL!")

except KeyboardInterrupt:
    print("\n⏸️  Download interrupted. You can resume by running this script again.")
except Exception as e:
    print(f"\n❌ Download failed: {e}")
    print("💡 Try running the script again - downloads can be resumed.")
