{"version": 3, "file": "ssr-BmTBcd2c.js", "sources": ["../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/dev.js", "../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/ssr.js"], "sourcesContent": ["import {\n\tcustom_event,\n\tappend,\n\tappend_hydration,\n\tinsert,\n\tinsert_hydration,\n\tdetach,\n\tlisten,\n\tattr\n} from './dom.js';\nimport { SvelteComponent } from './Component.js';\nimport { is_void } from '../../shared/utils/names.js';\nimport { VERSION } from '../../shared/version.js';\nimport { contenteditable_truthy_values } from './utils.js';\nimport { ensure_array_like } from './each.js';\n\n/**\n * @template T\n * @param {string} type\n * @param {T} [detail]\n * @returns {void}\n */\nexport function dispatch_dev(type, detail) {\n\tdocument.dispatchEvent(custom_event(type, { version: VERSION, ...detail }, { bubbles: true }));\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @returns {void}\n */\nexport function append_dev(target, node) {\n\tdispatch_dev('SvelteDOMInsert', { target, node });\n\tappend(target, node);\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @returns {void}\n */\nexport function append_hydration_dev(target, node) {\n\tdispatch_dev('SvelteDOMInsert', { target, node });\n\tappend_hydration(target, node);\n}\n\n/**\n * @param {Node} target\n * @param {Node} node\n * @param {Node} [anchor]\n * @returns {void}\n */\nexport function insert_dev(target, node, anchor) {\n\tdispatch_dev('SvelteDOMInsert', { target, node, anchor });\n\tinsert(target, node, anchor);\n}\n\n/** @param {Node} target\n * @param {Node} node\n * @param {Node} [anchor]\n * @returns {void}\n */\nexport function insert_hydration_dev(target, node, anchor) {\n\tdispatch_dev('SvelteDOMInsert', { target, node, anchor });\n\tinsert_hydration(target, node, anchor);\n}\n\n/**\n * @param {Node} node\n * @returns {void}\n */\nexport function detach_dev(node) {\n\tdispatch_dev('SvelteDOMRemove', { node });\n\tdetach(node);\n}\n\n/**\n * @param {Node} before\n * @param {Node} after\n * @returns {void}\n */\nexport function detach_between_dev(before, after) {\n\twhile (before.nextSibling && before.nextSibling !== after) {\n\t\tdetach_dev(before.nextSibling);\n\t}\n}\n\n/**\n * @param {Node} after\n * @returns {void}\n */\nexport function detach_before_dev(after) {\n\twhile (after.previousSibling) {\n\t\tdetach_dev(after.previousSibling);\n\t}\n}\n\n/**\n * @param {Node} before\n * @returns {void}\n */\nexport function detach_after_dev(before) {\n\twhile (before.nextSibling) {\n\t\tdetach_dev(before.nextSibling);\n\t}\n}\n\n/**\n * @param {Node} node\n * @param {string} event\n * @param {EventListenerOrEventListenerObject} handler\n * @param {boolean | AddEventListenerOptions | EventListenerOptions} [options]\n * @param {boolean} [has_prevent_default]\n * @param {boolean} [has_stop_propagation]\n * @param {boolean} [has_stop_immediate_propagation]\n * @returns {() => void}\n */\nexport function listen_dev(\n\tnode,\n\tevent,\n\thandler,\n\toptions,\n\thas_prevent_default,\n\thas_stop_propagation,\n\thas_stop_immediate_propagation\n) {\n\tconst modifiers =\n\t\toptions === true ? ['capture'] : options ? Array.from(Object.keys(options)) : [];\n\tif (has_prevent_default) modifiers.push('preventDefault');\n\tif (has_stop_propagation) modifiers.push('stopPropagation');\n\tif (has_stop_immediate_propagation) modifiers.push('stopImmediatePropagation');\n\tdispatch_dev('SvelteDOMAddEventListener', { node, event, handler, modifiers });\n\tconst dispose = listen(node, event, handler, options);\n\treturn () => {\n\t\tdispatch_dev('SvelteDOMRemoveEventListener', { node, event, handler, modifiers });\n\t\tdispose();\n\t};\n}\n\n/**\n * @param {Element} node\n * @param {string} attribute\n * @param {string} [value]\n * @returns {void}\n */\nexport function attr_dev(node, attribute, value) {\n\tattr(node, attribute, value);\n\tif (value == null) dispatch_dev('SvelteDOMRemoveAttribute', { node, attribute });\n\telse dispatch_dev('SvelteDOMSetAttribute', { node, attribute, value });\n}\n\n/**\n * @param {Element} node\n * @param {string} property\n * @param {any} [value]\n * @returns {void}\n */\nexport function prop_dev(node, property, value) {\n\tnode[property] = value;\n\tdispatch_dev('SvelteDOMSetProperty', { node, property, value });\n}\n\n/**\n * @param {HTMLElement} node\n * @param {string} property\n * @param {any} [value]\n * @returns {void}\n */\nexport function dataset_dev(node, property, value) {\n\tnode.dataset[property] = value;\n\tdispatch_dev('SvelteDOMSetDataset', { node, property, value });\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @returns {void}\n */\nexport function set_data_dev(text, data) {\n\tdata = '' + data;\n\tif (text.data === data) return;\n\tdispatch_dev('SvelteDOMSetData', { node: text, data });\n\ttext.data = /** @type {string} */ (data);\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @returns {void}\n */\nexport function set_data_contenteditable_dev(text, data) {\n\tdata = '' + data;\n\tif (text.wholeText === data) return;\n\tdispatch_dev('SvelteDOMSetData', { node: text, data });\n\ttext.data = /** @type {string} */ (data);\n}\n\n/**\n * @param {Text} text\n * @param {unknown} data\n * @param {string} attr_value\n * @returns {void}\n */\nexport function set_data_maybe_contenteditable_dev(text, data, attr_value) {\n\tif (~contenteditable_truthy_values.indexOf(attr_value)) {\n\t\tset_data_contenteditable_dev(text, data);\n\t} else {\n\t\tset_data_dev(text, data);\n\t}\n}\n\nexport function ensure_array_like_dev(arg) {\n\tif (\n\t\ttypeof arg !== 'string' &&\n\t\t!(arg && typeof arg === 'object' && 'length' in arg) &&\n\t\t!(typeof Symbol === 'function' && arg && Symbol.iterator in arg)\n\t) {\n\t\tthrow new Error('{#each} only works with iterable values.');\n\t}\n\treturn ensure_array_like(arg);\n}\n\n/**\n * @returns {void} */\nexport function validate_slots(name, slot, keys) {\n\tfor (const slot_key of Object.keys(slot)) {\n\t\tif (!~keys.indexOf(slot_key)) {\n\t\t\tconsole.warn(`<${name}> received an unexpected slot \"${slot_key}\".`);\n\t\t}\n\t}\n}\n\n/**\n * @param {unknown} tag\n * @returns {void}\n */\nexport function validate_dynamic_element(tag) {\n\tconst is_string = typeof tag === 'string';\n\tif (tag && !is_string) {\n\t\tthrow new Error('<svelte:element> expects \"this\" attribute to be a string.');\n\t}\n}\n\n/**\n * @param {undefined | string} tag\n * @returns {void}\n */\nexport function validate_void_dynamic_element(tag) {\n\tif (tag && is_void(tag)) {\n\t\tconsole.warn(`<svelte:element this=\"${tag}\"> is self-closing and cannot have content.`);\n\t}\n}\n\nexport function construct_svelte_component_dev(component, props) {\n\tconst error_message = 'this={...} of <svelte:component> should specify a Svelte component.';\n\ttry {\n\t\tconst instance = new component(props);\n\t\tif (!instance.$$ || !instance.$set || !instance.$on || !instance.$destroy) {\n\t\t\tthrow new Error(error_message);\n\t\t}\n\t\treturn instance;\n\t} catch (err) {\n\t\tconst { message } = err;\n\t\tif (typeof message === 'string' && message.indexOf('is not a constructor') !== -1) {\n\t\t\tthrow new Error(error_message);\n\t\t} else {\n\t\t\tthrow err;\n\t\t}\n\t}\n}\n\n/**\n * Base class for Svelte components with some minor dev-enhancements. Used when dev=true.\n *\n * Can be used to create strongly typed Svelte components.\n *\n * #### Example:\n *\n * You have component library on npm called `component-library`, from which\n * you export a component called `MyComponent`. For Svelte+TypeScript users,\n * you want to provide typings. Therefore you create a `index.d.ts`:\n * ```ts\n * import { SvelteComponent } from \"svelte\";\n * export class MyComponent extends SvelteComponent<{foo: string}> {}\n * ```\n * Typing this makes it possible for IDEs like VS Code with the Svelte extension\n * to provide intellisense and to use the component like this in a Svelte file\n * with TypeScript:\n * ```svelte\n * <script lang=\"ts\">\n * \timport { MyComponent } from \"component-library\";\n * </script>\n * <MyComponent foo={'bar'} />\n * ```\n * @template {Record<string, any>} [Props=any]\n * @template {Record<string, any>} [Events=any]\n * @template {Record<string, any>} [Slots=any]\n * @extends {SvelteComponent<Props, Events>}\n */\nexport class SvelteComponentDev extends SvelteComponent {\n\t/**\n\t * For type checking capabilities only.\n\t * Does not exist at runtime.\n\t * ### DO NOT USE!\n\t *\n\t * @type {Props}\n\t */\n\t$$prop_def;\n\t/**\n\t * For type checking capabilities only.\n\t * Does not exist at runtime.\n\t * ### DO NOT USE!\n\t *\n\t * @type {Events}\n\t */\n\t$$events_def;\n\t/**\n\t * For type checking capabilities only.\n\t * Does not exist at runtime.\n\t * ### DO NOT USE!\n\t *\n\t * @type {Slots}\n\t */\n\t$$slot_def;\n\n\t/** @param {import('./public.js').ComponentConstructorOptions<Props>} options */\n\tconstructor(options) {\n\t\tif (!options || (!options.target && !options.$$inline)) {\n\t\t\tthrow new Error(\"'target' is a required option\");\n\t\t}\n\t\tsuper();\n\t}\n\n\t/** @returns {void} */\n\t$destroy() {\n\t\tsuper.$destroy();\n\t\tthis.$destroy = () => {\n\t\t\tconsole.warn('Component was already destroyed'); // eslint-disable-line no-console\n\t\t};\n\t}\n\n\t/** @returns {void} */\n\t$capture_state() {}\n\n\t/** @returns {void} */\n\t$inject_state() {}\n}\n/**\n * @template {Record<string, any>} [Props=any]\n * @template {Record<string, any>} [Events=any]\n * @template {Record<string, any>} [Slots=any]\n * @deprecated Use `SvelteComponent` instead. See PR for more information: https://github.com/sveltejs/svelte/pull/8512\n * @extends {SvelteComponentDev<Props, Events, Slots>}\n */\nexport class SvelteComponentTyped extends SvelteComponentDev {}\n\n/** @returns {() => void} */\nexport function loop_guard(timeout) {\n\tconst start = Date.now();\n\treturn () => {\n\t\tif (Date.now() - start > timeout) {\n\t\t\tthrow new Error('Infinite loop detected');\n\t\t}\n\t};\n}\n", "export {\n\tonDestroy,\n\tsetContext,\n\tgetContext,\n\tgetAllContexts,\n\thasContext,\n\ttick,\n\tcreateEventDispatcher,\n\tSvelteComponent,\n\tSvelteComponentTyped\n} from './index.js';\n\n/** @returns {void} */\nexport function onMount() {}\n\n/** @returns {void} */\nexport function beforeUpdate() {}\n\n/** @returns {void} */\nexport function afterUpdate() {}\n"], "names": [], "mappings": ";;;AA+QA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,kBAAkB,SAAS,eAAe,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAU,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,YAAY,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAAU,CAAC;AACZ;AACA;AACA,CAAC,WAAW,CAAC,OAAO,EAAE;AACtB,EAAE,IAAI,CAAC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC1D,GAAG,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,KAAK,EAAE,CAAC;AACV,EAAE;AACF;AACA;AACA,CAAC,QAAQ,GAAG;AACZ,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC;AACnB,EAAE,IAAI,CAAC,QAAQ,GAAG,MAAM;AACxB,GAAG,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE;AACF;AACA;AACA,CAAC,cAAc,GAAG,EAAE;AACpB;AACA;AACA,CAAC,aAAa,GAAG,EAAE;AACnB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,oBAAoB,SAAS,kBAAkB,CAAC;;ACtV7D;AACO,SAAS,OAAO,GAAG,EAAE;AAC5B;AACA;AACO,SAAS,YAAY,GAAG,EAAE;AACjC;AACA;AACO,SAAS,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;", "x_google_ignoreList": [0, 1]}