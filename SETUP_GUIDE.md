# BAGEL Setup Guide

This guide will help you set up and run the BAGEL (Unified Model for Multimodal Understanding and Generation) project on your local machine.

## 🎯 What is BAGEL?

BAGEL is a powerful multimodal AI model that can:
- **Generate images from text** (Text-to-Image)
- **Edit images** based on text instructions
- **Understand images** and answer questions about them

## 📋 Prerequisites

- **Python 3.10 or higher**
- **At least 16GB RAM** (32GB recommended)
- **50GB+ free disk space** (for the model)
- **GPU with 8GB+ VRAM** (optional, but recommended for better performance)

## 🚀 Quick Setup

### Option 1: Automated Setup (Recommended)

1. **Run the setup script:**
   ```bash
   python setup_bagel.py
   ```
   This will:
   - Check your Python version
   - Install required dependencies
   - Optionally download the BAGEL model

### Option 2: Manual Setup

1. **Create a virtual environment (recommended):**
   ```bash
   python -m venv bagel_env
   
   # Windows
   bagel_env\Scripts\activate
   
   # Linux/Mac
   source bagel_env/bin/activate
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   pip install gradio
   ```

3. **Download the model:**
   ```bash
   python download_model.py
   ```

## 🎮 Running BAGEL

1. **Start the web interface:**
   ```bash
   python app.py
   ```

2. **Open your browser and go to:**
   ```
   http://127.0.0.1:7860
   ```

3. **Enjoy using BAGEL!** 🎉

## 🖥️ Web Interface Features

The web interface has three main tabs:

### 📝 Text to Image
- Enter a text prompt
- Adjust generation parameters
- Generate high-quality images

### 🖌️ Image Edit
- Upload an image
- Describe the changes you want
- Get an edited version of your image

### 🖼️ Image Understanding
- Upload an image
- Ask questions about the image
- Get detailed descriptions and analysis

## ⚙️ Configuration Options

### Model Path
By default, the model is downloaded to `models/BAGEL-7B-MoT`. You can change this by:
```bash
python app.py --model_path /path/to/your/model
```

### Server Settings
```bash
python app.py --server_name 0.0.0.0 --server_port 8080 --share
```

## 🔧 Troubleshooting

### Common Issues

1. **Out of Memory Error:**
   - Reduce batch size or image resolution
   - Use CPU-only mode if GPU memory is insufficient

2. **Model Download Fails:**
   - Check your internet connection
   - Ensure you have enough disk space
   - Try running `python download_model.py` again

3. **Dependencies Installation Issues:**
   - Make sure you're using Python 3.10+
   - Try updating pip: `pip install --upgrade pip`
   - Use a virtual environment

### Performance Tips

- **GPU Usage:** The model will automatically use GPU if available
- **CPU Mode:** Works but significantly slower
- **Memory:** Close other applications to free up RAM

## 📚 Additional Resources

- **Original Repository:** https://github.com/bytedance-seed/BAGEL
- **Paper:** https://arxiv.org/abs/2505.14683
- **Hugging Face Model:** https://huggingface.co/ByteDance-Seed/BAGEL-7B-MoT
- **Demo:** https://demo.bagel-ai.org/

## 🆘 Getting Help

If you encounter issues:
1. Check this guide first
2. Look at the original repository's issues
3. Make sure all dependencies are correctly installed
4. Verify the model was downloaded completely

## 📄 License

This project is licensed under the Apache 2.0 License.
