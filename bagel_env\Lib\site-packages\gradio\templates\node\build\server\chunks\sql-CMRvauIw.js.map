{"version": 3, "file": "sql-CMRvauIw.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/sql.js"], "sourcesContent": ["function sql(parserConfig) {\n  var client = parserConfig.client || {}, atoms = parserConfig.atoms || { \"false\": true, \"true\": true, \"null\": true }, builtin = parserConfig.builtin || set(defaultBuiltin), keywords = parserConfig.keywords || set(sqlKeywords), operatorChars = parserConfig.operatorChars || /^[*+\\-%<>!=&|~^\\/]/, support = parserConfig.support || {}, hooks = parserConfig.hooks || {}, dateSQL = parserConfig.dateSQL || { \"date\": true, \"time\": true, \"timestamp\": true }, backslashStringEscapes = parserConfig.backslashStringEscapes !== false, brackets = parserConfig.brackets || /^[\\{}\\(\\)\\[\\]]/, punctuation = parserConfig.punctuation || /^[;.,:]/;\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (hooks[ch]) {\n      var result = hooks[ch](stream, state);\n      if (result !== false)\n        return result;\n    }\n    if (support.hexNumber && (ch == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/) || (ch == \"x\" || ch == \"X\") && stream.match(/^'[0-9a-fA-F]*'/))) {\n      return \"number\";\n    } else if (support.binaryNumber && ((ch == \"b\" || ch == \"B\") && stream.match(/^'[01]+'/) || ch == \"0\" && stream.match(/^b[01]*/))) {\n      return \"number\";\n    } else if (ch.charCodeAt(0) > 47 && ch.charCodeAt(0) < 58) {\n      stream.match(/^[0-9]*(\\.[0-9]+)?([eE][-+]?[0-9]+)?/);\n      support.decimallessFloat && stream.match(/^\\.(?!\\.)/);\n      return \"number\";\n    } else if (ch == \"?\" && (stream.eatSpace() || stream.eol() || stream.eat(\";\"))) {\n      return \"macroName\";\n    } else if (ch == \"'\" || ch == '\"' && support.doubleQuote) {\n      state.tokenize = tokenLiteral(ch);\n      return state.tokenize(stream, state);\n    } else if ((support.nCharCast && (ch == \"n\" || ch == \"N\") || support.charsetCast && ch == \"_\" && stream.match(/[a-z][a-z0-9]*/i)) && (stream.peek() == \"'\" || stream.peek() == '\"')) {\n      return \"keyword\";\n    } else if (support.escapeConstant && (ch == \"e\" || ch == \"E\") && (stream.peek() == \"'\" || stream.peek() == '\"' && support.doubleQuote)) {\n      state.tokenize = function(stream2, state2) {\n        return (state2.tokenize = tokenLiteral(stream2.next(), true))(stream2, state2);\n      };\n      return \"keyword\";\n    } else if (support.commentSlashSlash && ch == \"/\" && stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (support.commentHash && ch == \"#\" || ch == \"-\" && stream.eat(\"-\") && (!support.commentSpaceRequired || stream.eat(\" \"))) {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (ch == \"/\" && stream.eat(\"*\")) {\n      state.tokenize = tokenComment(1);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\") {\n      if (support.zerolessFloat && stream.match(/^(?:\\d+(?:e[+-]?\\d+)?)/i))\n        return \"number\";\n      if (stream.match(/^\\.+/))\n        return null;\n      if (support.ODBCdotTable && stream.match(/^[\\w\\d_$#]+/))\n        return \"type\";\n    } else if (operatorChars.test(ch)) {\n      stream.eatWhile(operatorChars);\n      return \"operator\";\n    } else if (brackets.test(ch)) {\n      return \"bracket\";\n    } else if (punctuation.test(ch)) {\n      stream.eatWhile(punctuation);\n      return \"punctuation\";\n    } else if (ch == \"{\" && (stream.match(/^( )*(d|D|t|T|ts|TS)( )*'[^']*'( )*}/) || stream.match(/^( )*(d|D|t|T|ts|TS)( )*\"[^\"]*\"( )*}/))) {\n      return \"number\";\n    } else {\n      stream.eatWhile(/^[_\\w\\d]/);\n      var word = stream.current().toLowerCase();\n      if (dateSQL.hasOwnProperty(word) && (stream.match(/^( )+'[^']*'/) || stream.match(/^( )+\"[^\"]*\"/)))\n        return \"number\";\n      if (atoms.hasOwnProperty(word))\n        return \"atom\";\n      if (builtin.hasOwnProperty(word))\n        return \"type\";\n      if (keywords.hasOwnProperty(word))\n        return \"keyword\";\n      if (client.hasOwnProperty(word))\n        return \"builtin\";\n      return null;\n    }\n  }\n  function tokenLiteral(quote, backslashEscapes) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          state.tokenize = tokenBase;\n          break;\n        }\n        escaped = (backslashStringEscapes || backslashEscapes) && !escaped && ch == \"\\\\\";\n      }\n      return \"string\";\n    };\n  }\n  function tokenComment(depth) {\n    return function(stream, state) {\n      var m = stream.match(/^.*?(\\/\\*|\\*\\/)/);\n      if (!m)\n        stream.skipToEnd();\n      else if (m[1] == \"/*\")\n        state.tokenize = tokenComment(depth + 1);\n      else if (depth > 1)\n        state.tokenize = tokenComment(depth - 1);\n      else\n        state.tokenize = tokenBase;\n      return \"comment\";\n    };\n  }\n  function pushContext(stream, state, type) {\n    state.context = {\n      prev: state.context,\n      indent: stream.indentation(),\n      col: stream.column(),\n      type\n    };\n  }\n  function popContext(state) {\n    state.indent = state.context.indent;\n    state.context = state.context.prev;\n  }\n  return {\n    name: \"sql\",\n    startState: function() {\n      return { tokenize: tokenBase, context: null };\n    },\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (state.context && state.context.align == null)\n          state.context.align = false;\n      }\n      if (state.tokenize == tokenBase && stream.eatSpace())\n        return null;\n      var style = state.tokenize(stream, state);\n      if (style == \"comment\")\n        return style;\n      if (state.context && state.context.align == null)\n        state.context.align = true;\n      var tok = stream.current();\n      if (tok == \"(\")\n        pushContext(stream, state, \")\");\n      else if (tok == \"[\")\n        pushContext(stream, state, \"]\");\n      else if (state.context && state.context.type == tok)\n        popContext(state);\n      return style;\n    },\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context;\n      if (!cx)\n        return null;\n      var closing = textAfter.charAt(0) == cx.type;\n      if (cx.align)\n        return cx.col + (closing ? 0 : 1);\n      else\n        return cx.indent + (closing ? 0 : iCx.unit);\n    },\n    languageData: {\n      commentTokens: {\n        line: support.commentSlashSlash ? \"//\" : support.commentHash ? \"#\" : \"--\",\n        block: { open: \"/*\", close: \"*/\" }\n      },\n      closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"] }\n    }\n  };\n}\nfunction hookIdentifier(stream) {\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == \"`\" && !stream.eat(\"`\"))\n      return \"string.special\";\n  }\n  stream.backUp(stream.current().length - 1);\n  return stream.eatWhile(/\\w/) ? \"string.special\" : null;\n}\nfunction hookIdentifierDoublequote(stream) {\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == '\"' && !stream.eat('\"'))\n      return \"string.special\";\n  }\n  stream.backUp(stream.current().length - 1);\n  return stream.eatWhile(/\\w/) ? \"string.special\" : null;\n}\nfunction hookVar(stream) {\n  if (stream.eat(\"@\")) {\n    stream.match(\"session.\");\n    stream.match(\"local.\");\n    stream.match(\"global.\");\n  }\n  if (stream.eat(\"'\")) {\n    stream.match(/^.*'/);\n    return \"string.special\";\n  } else if (stream.eat('\"')) {\n    stream.match(/^.*\"/);\n    return \"string.special\";\n  } else if (stream.eat(\"`\")) {\n    stream.match(/^.*`/);\n    return \"string.special\";\n  } else if (stream.match(/^[0-9a-zA-Z$\\.\\_]+/)) {\n    return \"string.special\";\n  }\n  return null;\n}\nfunction hookClient(stream) {\n  if (stream.eat(\"N\")) {\n    return \"atom\";\n  }\n  return stream.match(/^[a-zA-Z.#!?]/) ? \"string.special\" : null;\n}\nvar sqlKeywords = \"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit \";\nfunction set(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i)\n    obj[words[i]] = true;\n  return obj;\n}\nvar defaultBuiltin = \"bool boolean bit blob enum long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision real date datetime year unsigned signed decimal numeric\";\nconst standardSQL = sql({\n  keywords: set(sqlKeywords + \"begin\"),\n  builtin: set(defaultBuiltin),\n  atoms: set(\"false true null unknown\"),\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote binaryNumber hexNumber\")\n});\nconst msSQL = sql({\n  client: set(\"$partition binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\"),\n  keywords: set(sqlKeywords + \"begin trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx updlock with\"),\n  builtin: set(\"bigint numeric bit smallint decimal smallmoney int tinyint money float real char varchar text nchar nvarchar ntext binary varbinary image cursor timestamp hierarchyid uniqueidentifier sql_variant xml table \"),\n  atoms: set(\"is not null like and or in left right between inner outer join all any some cross unpivot pivot exists\"),\n  operatorChars: /^[*+\\-%<>!=^\\&|\\/]/,\n  brackets: /^[\\{}\\(\\)]/,\n  punctuation: /^[;.,:/]/,\n  backslashStringEscapes: false,\n  dateSQL: set(\"date datetimeoffset datetime2 smalldatetime datetime time\"),\n  hooks: {\n    \"@\": hookVar\n  }\n});\nconst mySQL = sql({\n  client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n  keywords: set(sqlKeywords + \"accessible action add after algorithm all analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general get global grant grants group group_concat handler hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show signal slave slow smallint snapshot soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n  hooks: {\n    \"@\": hookVar,\n    \"`\": hookIdentifier,\n    \"\\\\\": hookClient\n  }\n});\nconst mariaDB = sql({\n  client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n  keywords: set(sqlKeywords + \"accessible action add after algorithm all always analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general generated get global grant grants group group_concat handler hard hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password persistent phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show shutdown signal slave slow smallint snapshot soft soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views virtual warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n  hooks: {\n    \"@\": hookVar,\n    \"`\": hookIdentifier,\n    \"\\\\\": hookClient\n  }\n});\nconst sqlite = sql({\n  // commands of the official SQLite client, ref: https://www.sqlite.org/cli.html#dotcmd\n  client: set(\"auth backup bail binary changes check clone databases dbinfo dump echo eqp exit explain fullschema headers help import imposter indexes iotrace limit lint load log mode nullvalue once open output print prompt quit read restore save scanstats schema separator session shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\"),\n  // ref: http://sqlite.org/lang_keywords.html\n  keywords: set(sqlKeywords + \"abort action add after all analyze attach autoincrement before begin cascade case cast check collate column commit conflict constraint cross current_date current_time current_timestamp database default deferrable deferred detach each else end escape except exclusive exists explain fail for foreign full glob if ignore immediate index indexed initially inner instead intersect isnull key left limit match natural no notnull null of offset outer plan pragma primary query raise recursive references regexp reindex release rename replace restrict right rollback row savepoint temp temporary then to transaction trigger unique using vacuum view virtual when with without\"),\n  // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text clob bigint int int2 int8 integer float double char varchar date datetime year unsigned signed numeric real\"),\n  // ref: http://sqlite.org/syntax/literal-value.html\n  atoms: set(\"null current_date current_time current_timestamp\"),\n  // ref: http://sqlite.org/lang_expr.html#binaryops\n  operatorChars: /^[*+\\-%<>!=&|/~]/,\n  // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n  dateSQL: set(\"date time timestamp datetime\"),\n  support: set(\"decimallessFloat zerolessFloat\"),\n  identifierQuote: '\"',\n  //ref: http://sqlite.org/lang_keywords.html\n  hooks: {\n    // bind-parameters ref:http://sqlite.org/lang_expr.html#varparam\n    \"@\": hookVar,\n    \":\": hookVar,\n    \"?\": hookVar,\n    \"$\": hookVar,\n    // The preferred way to escape Identifiers is using double quotes, ref: http://sqlite.org/lang_keywords.html\n    '\"': hookIdentifierDoublequote,\n    // there is also support for backticks, ref: http://sqlite.org/lang_keywords.html\n    \"`\": hookIdentifier\n  }\n});\nconst cassandra = sql({\n  client: {},\n  keywords: set(\"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime\"),\n  builtin: set(\"ascii bigint blob boolean counter decimal double float frozen inet int list map static text timestamp timeuuid tuple uuid varchar varint\"),\n  atoms: set(\"false true infinity NaN\"),\n  operatorChars: /^[<>=]/,\n  dateSQL: {},\n  support: set(\"commentSlashSlash decimallessFloat\"),\n  hooks: {}\n});\nconst plSQL = sql({\n  client: set(\"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define describe echo editfile embedded escape exec execute feedback flagger flush heading headsep instance linesize lno loboffset logsource long longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar release repfooter repheader serveroutput shiftinout show showmode size spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout time timing trimout trimspool ttitle underline verify version wrap\"),\n  keywords: set(\"abort accept access add all alter and any array arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body boolean by case cast char char_base check close cluster clusters colauth column comment commit compress connect connected constant constraint crash create current currval cursor data_base database date dba deallocate debugoff debugon decimal declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry escape exception exception_init exchange exclusive exists exit external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging long loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base object of off offline on online only open option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw read rebuild record ref references refresh release rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate session set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\"),\n  builtin: set(\"abs acos add_months ascii asin atan atan2 average bfile bfilename bigserial bit blob ceil character chartorowid chr clob concat convert cos cosh count dec decode deref dual dump dup_val_on_index empty error exp false float floor found glb greatest hextoraw initcap instr instrb int integer isopen last_day least length lengthb ln lower lpad ltrim lub make_ref max min mlslabel mod months_between natural naturaln nchar nclob new_time next_day nextval nls_charset_decl_len nls_charset_id nls_charset_name nls_initcap nls_lower nls_sort nls_upper nlssort no_data_found notfound null number numeric nvarchar2 nvl others power rawtohex real reftohex round rowcount rowidtochar rowtype rpad rtrim serial sign signtype sin sinh smallint soundex sqlcode sqlerrm sqrt stddev string substr substrb sum sysdate tan tanh to_char text to_date to_label to_multi_byte to_number to_single_byte translate true trunc uid unlogged upper user userenv varchar varchar2 variance varying vsize xml\"),\n  operatorChars: /^[*\\/+\\-%<>!=~]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"doubleQuote nCharCast zerolessFloat binaryNumber hexNumber\")\n});\nconst hive = sql({\n  keywords: set(\"select alter $elem$ $key$ $value$ add after all analyze and archive as asc before between binary both bucket buckets by cascade case cast change cluster clustered clusterstatus collection column columns comment compute concatenate continue create cross cursor data database databases dbproperties deferred delete delimited desc describe directory disable distinct distribute drop else enable end escaped exclusive exists explain export extended external fetch fields fileformat first format formatted from full function functions grant group having hold_ddltime idxproperties if import in index indexes inpath inputdriver inputformat insert intersect into is items join keys lateral left like limit lines load local location lock locks mapjoin materialized minus msck no_drop nocompress not of offline on option or order out outer outputdriver outputformat overwrite partition partitioned partitions percent plus preserve procedure purge range rcfile read readonly reads rebuild recordreader recordwriter recover reduce regexp rename repair replace restrict revoke right rlike row schema schemas semi sequencefile serde serdeproperties set shared show show_database sort sorted ssl statistics stored streamtable table tables tablesample tblproperties temporary terminated textfile then tmp to touch transform trigger unarchive undo union uniquejoin unlock update use using utc utc_tmestamp view when where while with admin authorization char compact compactions conf cube current current_date current_timestamp day decimal defined dependency directories elem_type exchange file following for grouping hour ignore inner interval jar less logical macro minute month more none noscan over owner partialscan preceding pretty principals protection reload rewrite role roles rollup rows second server sets skewed transactions truncate unbounded unset uri user values window year\"),\n  builtin: set(\"bool boolean long timestamp tinyint smallint bigint int float double date datetime unsigned string array struct map uniontype key_type utctimestamp value_type varchar\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=]/,\n  dateSQL: set(\"date timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote binaryNumber hexNumber\")\n});\nconst pgSQL = sql({\n  client: set(\"source\"),\n  // For PostgreSQL - https://www.postgresql.org/docs/11/sql-keywords-appendix.html\n  // For pl/pgsql lang - https://github.com/postgres/postgres/blob/REL_11_2/src/pl/plpgsql/src/pl_scanner.c\n  keywords: set(sqlKeywords + \"a abort abs absent absolute access according action ada add admin after aggregate alias all allocate also alter always analyse analyze and any are array array_agg array_max_cardinality as asc asensitive assert assertion assignment asymmetric at atomic attach attribute attributes authorization avg backward base64 before begin begin_frame begin_partition bernoulli between bigint binary bit bit_length blob blocked bom boolean both breadth by c cache call called cardinality cascade cascaded case cast catalog catalog_name ceil ceiling chain char char_length character character_length character_set_catalog character_set_name character_set_schema characteristics characters check checkpoint class class_origin clob close cluster coalesce cobol collate collation collation_catalog collation_name collation_schema collect column column_name columns command_function command_function_code comment comments commit committed concurrently condition condition_number configuration conflict connect connection connection_name constant constraint constraint_catalog constraint_name constraint_schema constraints constructor contains content continue control conversion convert copy corr corresponding cost count covar_pop covar_samp create cross csv cube cume_dist current current_catalog current_date current_default_transform_group current_path current_role current_row current_schema current_time current_timestamp current_transform_group_for_type current_user cursor cursor_name cycle data database datalink datatype date datetime_interval_code datetime_interval_precision day db deallocate debug dec decimal declare default defaults deferrable deferred defined definer degree delete delimiter delimiters dense_rank depends depth deref derived desc describe descriptor detach detail deterministic diagnostics dictionary disable discard disconnect dispatch distinct dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue do document domain double drop dump dynamic dynamic_function dynamic_function_code each element else elseif elsif empty enable encoding encrypted end end_frame end_partition endexec enforced enum equals errcode error escape event every except exception exclude excluding exclusive exec execute exists exit exp explain expression extension external extract false family fetch file filter final first first_value flag float floor following for force foreach foreign fortran forward found frame_row free freeze from fs full function functions fusion g general generated get global go goto grant granted greatest group grouping groups handler having header hex hierarchy hint hold hour id identity if ignore ilike immediate immediately immutable implementation implicit import in include including increment indent index indexes indicator info inherit inherits initially inline inner inout input insensitive insert instance instantiable instead int integer integrity intersect intersection interval into invoker is isnull isolation join k key key_member key_type label lag language large last last_value lateral lead leading leakproof least left length level library like like_regex limit link listen ln load local localtime localtimestamp location locator lock locked log logged loop lower m map mapping match matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text method min minute minvalue mod mode modifies module month more move multiset mumps name names namespace national natural nchar nclob nesting new next nfc nfd nfkc nfkd nil no none normalize normalized not nothing notice notify notnull nowait nth_value ntile null nullable nullif nulls number numeric object occurrences_regex octet_length octets of off offset oids old on only open operator option options or order ordering ordinality others out outer output over overlaps overlay overriding owned owner p pad parallel parameter parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partial partition pascal passing passthrough password path percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding precision prepare prepared preserve primary print_strict_params prior privileges procedural procedure procedures program public publication query quote raise range rank read reads real reassign recheck recovery recursive ref references referencing refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex relative release rename repeatable replace replica requiring reset respect restart restore restrict result result_oid return returned_cardinality returned_length returned_octet_length returned_sqlstate returning returns reverse revoke right role rollback rollup routine routine_catalog routine_name routine_schema routines row row_count row_number rows rowtype rule savepoint scale schema schema_name schemas scope scope_catalog scope_name scope_schema scroll search second section security select selective self sensitive sequence sequences serializable server server_name session session_user set setof sets share show similar simple size skip slice smallint snapshot some source space specific specific_name specifictype sql sqlcode sqlerror sqlexception sqlstate sqlwarning sqrt stable stacked standalone start state statement static statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time system_user t table table_name tables tablesample tablespace temp template temporary text then ties time timestamp timezone_hour timezone_minute to token top_level_count trailing transaction transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex translation treat trigger trigger_catalog trigger_name trigger_schema trim trim_array true truncate trusted type types uescape unbounded uncommitted under unencrypted union unique unknown unlink unlisten unlogged unnamed unnest until untyped update upper uri usage use_column use_variable user user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema using vacuum valid validate validator value value_of values var_pop var_samp varbinary varchar variable_conflict variadic varying verbose version versioning view views volatile warning when whenever where while whitespace width_bucket window with within without work wrapper write xml xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate year yes zone\"),\n  // https://www.postgresql.org/docs/11/datatype.html\n  builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*\\/+\\-%<>!=&|^\\/#@?~]/,\n  backslashStringEscapes: false,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast escapeConstant\")\n});\nconst gql = sql({\n  keywords: set(\"ancestor and asc by contains desc descendant distinct from group has in is limit offset on order select superset where\"),\n  atoms: set(\"false true\"),\n  builtin: set(\"blob datetime first key __key__ string integer double boolean null\"),\n  operatorChars: /^[*+\\-%<>!=]/\n});\nconst gpSQL = sql({\n  client: set(\"source\"),\n  //https://github.com/greenplum-db/gpdb/blob/master/src/include/parser/kwlist.h\n  keywords: set(\"abort absolute access action active add admin after aggregate all also alter always analyse analyze and any array as asc assertion assignment asymmetric at authorization backward before begin between bigint binary bit boolean both by cache called cascade cascaded case cast chain char character characteristics check checkpoint class close cluster coalesce codegen collate column comment commit committed concurrency concurrently configuration connection constraint constraints contains content continue conversion copy cost cpu_rate_limit create createdb createexttable createrole createuser cross csv cube current current_catalog current_date current_role current_schema current_time current_timestamp current_user cursor cycle data database day deallocate dec decimal declare decode default defaults deferrable deferred definer delete delimiter delimiters deny desc dictionary disable discard distinct distributed do document domain double drop dxl each else enable encoding encrypted end enum errors escape every except exchange exclude excluding exclusive execute exists explain extension external extract false family fetch fields filespace fill filter first float following for force foreign format forward freeze from full function global grant granted greatest group group_id grouping handler hash having header hold host hour identity if ignore ilike immediate immutable implicit in including inclusive increment index indexes inherit inherits initially inline inner inout input insensitive insert instead int integer intersect interval into invoker is isnull isolation join key language large last leading least left level like limit list listen load local localtime localtimestamp location lock log login mapping master match maxvalue median merge minute minvalue missing mode modifies modify month move name names national natural nchar new newline next no nocreatedb nocreateexttable nocreaterole nocreateuser noinherit nologin none noovercommit nosuperuser not nothing notify notnull nowait null nullif nulls numeric object of off offset oids old on only operator option options or order ordered others out outer over overcommit overlaps overlay owned owner parser partial partition partitions passing password percent percentile_cont percentile_disc placing plans position preceding precision prepare prepared preserve primary prior privileges procedural procedure protocol queue quote randomly range read readable reads real reassign recheck recursive ref references reindex reject relative release rename repeatable replace replica reset resource restart restrict returning returns revoke right role rollback rollup rootpartition row rows rule savepoint scatter schema scroll search second security segment select sequence serializable session session_user set setof sets share show similar simple smallint some split sql stable standalone start statement statistics stdin stdout storage strict strip subpartition subpartitions substring superuser symmetric sysid system table tablespace temp template temporary text then threshold ties time timestamp to trailing transaction treat trigger trim true truncate trusted type unbounded uncommitted unencrypted union unique unknown unlisten until update user using vacuum valid validation validator value values varchar variadic varying verbose version view volatile web when where whitespace window with within without work writable write xml xmlattributes xmlconcat xmlelement xmlexists xmlforest xmlparse xmlpi xmlroot xmlserialize year yes zone\"),\n  builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast\")\n});\nconst sparkSQL = sql({\n  keywords: set(\"add after all alter analyze and anti archive array as asc at between bucket buckets by cache cascade case cast change clear cluster clustered codegen collection column columns comment commit compact compactions compute concatenate cost create cross cube current current_date current_timestamp database databases data dbproperties defined delete delimited deny desc describe dfs directories distinct distribute drop else end escaped except exchange exists explain export extended external false fields fileformat first following for format formatted from full function functions global grant group grouping having if ignore import in index indexes inner inpath inputformat insert intersect interval into is items join keys last lateral lazy left like limit lines list load local location lock locks logical macro map minus msck natural no not null nulls of on optimize option options or order out outer outputformat over overwrite partition partitioned partitions percent preceding principals purge range recordreader recordwriter recover reduce refresh regexp rename repair replace reset restrict revoke right rlike role roles rollback rollup row rows schema schemas select semi separated serde serdeproperties set sets show skewed sort sorted start statistics stored stratify struct table tables tablesample tblproperties temp temporary terminated then to touch transaction transactions transform true truncate unarchive unbounded uncache union unlock unset use using values view when where window with\"),\n  builtin: set(\"tinyint smallint int bigint boolean float double string binary timestamp decimal array map struct uniontype delimited serde sequencefile textfile rcfile inputformat outputformat\"),\n  atoms: set(\"false true null\"),\n  operatorChars: /^[*\\/+\\-%<>!=~&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote zerolessFloat\")\n});\nconst esper = sql({\n  client: set(\"source\"),\n  // http://www.espertech.com/esper/release-5.5.0/esper-reference/html/appendix_keywords.html\n  keywords: set(\"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit after all and as at asc avedev avg between by case cast coalesce count create current_timestamp day days delete define desc distinct else end escape events every exists false first from full group having hour hours in inner insert instanceof into irstream is istream join last lastweekday left limit like max match_recognize matches median measures metadatasql min minute minutes msec millisecond milliseconds not null offset on or order outer output partition pattern prev prior regexp retain-union retain-intersection right rstream sec second seconds select set some snapshot sql stddev sum then true unidirectional until update variable weekday when where window\"),\n  builtin: {},\n  atoms: set(\"false true null\"),\n  operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n  dateSQL: set(\"time\"),\n  support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber\")\n});\nexport {\n  cassandra,\n  esper,\n  gpSQL,\n  gql,\n  hive,\n  mariaDB,\n  msSQL,\n  mySQL,\n  pgSQL,\n  plSQL,\n  sparkSQL,\n  sql,\n  sqlite,\n  standardSQL\n};\n"], "names": [], "mappings": "AAAA,SAAS,GAAG,CAAC,YAAY,EAAE;AAC3B,EAAE,IAAI,MAAM,GAAG,YAAY,CAAC,MAAM,IAAI,EAAE,EAAE,KAAK,GAAG,YAAY,CAAC,KAAK,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,GAAG,YAAY,CAAC,OAAO,IAAI,GAAG,CAAC,cAAc,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,EAAE,aAAa,GAAG,YAAY,CAAC,aAAa,IAAI,oBAAoB,EAAE,OAAO,GAAG,YAAY,CAAC,OAAO,IAAI,EAAE,EAAE,KAAK,GAAG,YAAY,CAAC,KAAK,IAAI,EAAE,EAAE,OAAO,GAAG,YAAY,CAAC,OAAO,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,KAAK,KAAK,EAAE,QAAQ,GAAG,YAAY,CAAC,QAAQ,IAAI,gBAAgB,EAAE,WAAW,GAAG,YAAY,CAAC,WAAW,IAAI,SAAS,CAAC;AACvnB,EAAE,SAAS,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE;AACpC,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAC3B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;AACnB,MAAM,IAAI,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5C,MAAM,IAAI,MAAM,KAAK,KAAK;AAC1B,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,KAAK,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,EAAE;AAC9I,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,OAAO,CAAC,YAAY,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,KAAK,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE;AACvI,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE;AAC/D,MAAM,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;AAC3D,MAAM,OAAO,CAAC,gBAAgB,IAAI,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC5D,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AACpF,MAAM,OAAO,WAAW,CAAC;AACzB,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,OAAO,CAAC,WAAW,EAAE;AAC9D,MAAM,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC;AACxC,MAAM,OAAO,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3C,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC,IAAI,OAAO,CAAC,WAAW,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,MAAM,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,EAAE;AACzL,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,OAAO,CAAC,cAAc,KAAK,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE;AAC5I,MAAM,KAAK,CAAC,QAAQ,GAAG,SAAS,OAAO,EAAE,MAAM,EAAE;AACjD,QAAQ,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AACvF,OAAO,CAAC;AACR,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,OAAO,CAAC,iBAAiB,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1E,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,OAAO,CAAC,WAAW,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AACvI,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC7C,MAAM,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACvC,MAAM,OAAO,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC3C,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE;AAC1B,MAAM,IAAI,OAAO,CAAC,aAAa,IAAI,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC;AAC1E,QAAQ,OAAO,QAAQ,CAAC;AACxB,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAC9B,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,IAAI,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC;AAC7D,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACvC,MAAM,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AACrC,MAAM,OAAO,UAAU,CAAC;AACxB,KAAK,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAClC,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACrC,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACnC,MAAM,OAAO,aAAa,CAAC;AAC3B,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,KAAK,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC,EAAE;AAC5I,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAClC,MAAM,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,WAAW,EAAE,CAAC;AAChD,MAAM,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACxG,QAAQ,OAAO,QAAQ,CAAC;AACxB,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;AACpC,QAAQ,OAAO,MAAM,CAAC;AACtB,MAAM,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;AACtC,QAAQ,OAAO,MAAM,CAAC;AACtB,MAAM,IAAI,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC;AACvC,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;AACrC,QAAQ,OAAO,SAAS,CAAC;AACzB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE,gBAAgB,EAAE;AACjD,IAAI,OAAO,SAAS,MAAM,EAAE,KAAK,EAAE;AACnC,MAAM,IAAI,OAAO,GAAG,KAAK,EAAE,EAAE,CAAC;AAC9B,MAAM,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;AAC3C,QAAQ,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,OAAO,EAAE;AACrC,UAAU,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC;AACrC,UAAU,MAAM;AAChB,SAAS;AACT,QAAQ,OAAO,GAAG,CAAC,sBAAsB,IAAI,gBAAgB,KAAK,CAAC,OAAO,IAAI,EAAE,IAAI,IAAI,CAAC;AACzF,OAAO;AACP,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE;AAC/B,IAAI,OAAO,SAAS,MAAM,EAAE,KAAK,EAAE;AACnC,MAAM,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC9C,MAAM,IAAI,CAAC,CAAC;AACZ,QAAQ,MAAM,CAAC,SAAS,EAAE,CAAC;AAC3B,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAC3B,QAAQ,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACjD,WAAW,IAAI,KAAK,GAAG,CAAC;AACxB,QAAQ,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACjD;AACA,QAAQ,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC;AACnC,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE;AAC5C,IAAI,KAAK,CAAC,OAAO,GAAG;AACpB,MAAM,IAAI,EAAE,KAAK,CAAC,OAAO;AACzB,MAAM,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;AAClC,MAAM,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE;AAC1B,MAAM,IAAI;AACV,KAAK,CAAC;AACN,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE;AAC7B,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AACxC,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AACvC,GAAG;AACH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,UAAU,EAAE,WAAW;AAC3B,MAAM,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACpD,KAAK;AACL,IAAI,KAAK,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;AACnC,MAAM,IAAI,MAAM,CAAC,GAAG,EAAE,EAAE;AACxB,QAAQ,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI;AACxD,UAAU,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACtC,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,SAAS,IAAI,MAAM,CAAC,QAAQ,EAAE;AAC1D,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAChD,MAAM,IAAI,KAAK,IAAI,SAAS;AAC5B,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI;AACtD,QAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACnC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;AACjC,MAAM,IAAI,GAAG,IAAI,GAAG;AACpB,QAAQ,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACxC,WAAW,IAAI,GAAG,IAAI,GAAG;AACzB,QAAQ,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;AACxC,WAAW,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI,GAAG;AACzD,QAAQ,UAAU,CAAC,KAAK,CAAC,CAAC;AAC1B,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,MAAM,EAAE,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE;AAC5C,MAAM,IAAI,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC;AAC7B,MAAM,IAAI,CAAC,EAAE;AACb,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;AACnD,MAAM,IAAI,EAAE,CAAC,KAAK;AAClB,QAAQ,OAAO,EAAE,CAAC,GAAG,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1C;AACA,QAAQ,OAAO,EAAE,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,YAAY,EAAE;AAClB,MAAM,aAAa,EAAE;AACrB,QAAQ,IAAI,EAAE,OAAO,CAAC,iBAAiB,GAAG,IAAI,GAAG,OAAO,CAAC,WAAW,GAAG,GAAG,GAAG,IAAI;AACjF,QAAQ,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE;AAC1C,OAAO;AACP,MAAM,aAAa,EAAE,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;AACjE,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,cAAc,CAAC,MAAM,EAAE;AAChC,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;AACvC,IAAI,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACrC,MAAM,OAAO,gBAAgB,CAAC;AAC9B,GAAG;AACH,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,EAAE,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,IAAI,CAAC;AACzD,CAAC;AACD,SAAS,yBAAyB,CAAC,MAAM,EAAE;AAC3C,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;AACvC,IAAI,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACrC,MAAM,OAAO,gBAAgB,CAAC;AAC9B,GAAG;AACH,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7C,EAAE,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,IAAI,CAAC;AACzD,CAAC;AACD,SAAS,OAAO,CAAC,MAAM,EAAE;AACzB,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACvB,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC7B,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC5B,GAAG;AACH,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACvB,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACzB,IAAI,OAAO,gBAAgB,CAAC;AAC5B,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC9B,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACzB,IAAI,OAAO,gBAAgB,CAAC;AAC5B,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC9B,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACzB,IAAI,OAAO,gBAAgB,CAAC;AAC5B,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE;AACjD,IAAI,OAAO,gBAAgB,CAAC;AAC5B,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,SAAS,UAAU,CAAC,MAAM,EAAE;AAC5B,EAAE,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACvB,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,gBAAgB,GAAG,IAAI,CAAC;AACjE,CAAC;AACD,IAAI,WAAW,GAAG,oLAAoL,CAAC;AACvM,SAAS,GAAG,CAAC,GAAG,EAAE;AAClB,EAAE,IAAI,GAAG,GAAG,EAAE,EAAE,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;AACvC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACzB,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,IAAI,cAAc,GAAG,qTAAqT,CAAC;AACtU,MAAC,WAAW,GAAG,GAAG,CAAC;AACxB,EAAE,QAAQ,EAAE,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC;AACtC,EAAE,OAAO,EAAE,GAAG,CAAC,cAAc,CAAC;AAC9B,EAAE,KAAK,EAAE,GAAG,CAAC,yBAAyB,CAAC;AACvC,EAAE,OAAO,EAAE,GAAG,CAAC,qBAAqB,CAAC;AACrC,EAAE,OAAO,EAAE,GAAG,CAAC,iDAAiD,CAAC;AACjE,CAAC,EAAE;AACE,MAAC,KAAK,GAAG,GAAG,CAAC;AAClB,EAAE,MAAM,EAAE,GAAG,CAAC,uVAAuV,CAAC;AACtW,EAAE,QAAQ,EAAE,GAAG,CAAC,WAAW,GAAG,qSAAqS,CAAC;AACpU,EAAE,OAAO,EAAE,GAAG,CAAC,gNAAgN,CAAC;AAChO,EAAE,KAAK,EAAE,GAAG,CAAC,wGAAwG,CAAC;AACtH,EAAE,aAAa,EAAE,oBAAoB;AACrC,EAAE,QAAQ,EAAE,YAAY;AACxB,EAAE,WAAW,EAAE,UAAU;AACzB,EAAE,sBAAsB,EAAE,KAAK;AAC/B,EAAE,OAAO,EAAE,GAAG,CAAC,2DAA2D,CAAC;AAC3E,EAAE,KAAK,EAAE;AACT,IAAI,GAAG,EAAE,OAAO;AAChB,GAAG;AACH,CAAC,EAAE;AACE,MAAC,KAAK,GAAG,GAAG,CAAC;AAClB,EAAE,MAAM,EAAE,GAAG,CAAC,6HAA6H,CAAC;AAC5I,EAAE,QAAQ,EAAE,GAAG,CAAC,WAAW,GAAG,o9FAAo9F,CAAC;AACn/F,EAAE,OAAO,EAAE,GAAG,CAAC,wTAAwT,CAAC;AACxU,EAAE,KAAK,EAAE,GAAG,CAAC,yBAAyB,CAAC;AACvC,EAAE,aAAa,EAAE,iBAAiB;AAClC,EAAE,OAAO,EAAE,GAAG,CAAC,qBAAqB,CAAC;AACrC,EAAE,OAAO,EAAE,GAAG,CAAC,uIAAuI,CAAC;AACvJ,EAAE,KAAK,EAAE;AACT,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,GAAG,EAAE,cAAc;AACvB,IAAI,IAAI,EAAE,UAAU;AACpB,GAAG;AACH,CAAC,EAAE;AACE,MAAC,OAAO,GAAG,GAAG,CAAC;AACpB,EAAE,MAAM,EAAE,GAAG,CAAC,6HAA6H,CAAC;AAC5I,EAAE,QAAQ,EAAE,GAAG,CAAC,WAAW,GAAG,2gGAA2gG,CAAC;AAC1iG,EAAE,OAAO,EAAE,GAAG,CAAC,wTAAwT,CAAC;AACxU,EAAE,KAAK,EAAE,GAAG,CAAC,yBAAyB,CAAC;AACvC,EAAE,aAAa,EAAE,iBAAiB;AAClC,EAAE,OAAO,EAAE,GAAG,CAAC,qBAAqB,CAAC;AACrC,EAAE,OAAO,EAAE,GAAG,CAAC,uIAAuI,CAAC;AACvJ,EAAE,KAAK,EAAE;AACT,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,GAAG,EAAE,cAAc;AACvB,IAAI,IAAI,EAAE,UAAU;AACpB,GAAG;AACH,CAAC,EAAE;AACE,MAAC,MAAM,GAAG,GAAG,CAAC;AACnB;AACA,EAAE,MAAM,EAAE,GAAG,CAAC,sWAAsW,CAAC;AACrX;AACA,EAAE,QAAQ,EAAE,GAAG,CAAC,WAAW,GAAG,6pBAA6pB,CAAC;AAC5rB;AACA,EAAE,OAAO,EAAE,GAAG,CAAC,oQAAoQ,CAAC;AACpR;AACA,EAAE,KAAK,EAAE,GAAG,CAAC,kDAAkD,CAAC;AAChE;AACA,EAAE,aAAa,EAAE,kBAAkB;AACnC;AACA,EAAE,OAAO,EAAE,GAAG,CAAC,8BAA8B,CAAC;AAC9C,EAAE,OAAO,EAAE,GAAG,CAAC,gCAAgC,CAAC;AAChD,EAAE,eAAe,EAAE,GAAG;AACtB;AACA,EAAE,KAAK,EAAE;AACT;AACA,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,GAAG,EAAE,OAAO;AAChB;AACA,IAAI,GAAG,EAAE,yBAAyB;AAClC;AACA,IAAI,GAAG,EAAE,cAAc;AACvB,GAAG;AACH,CAAC,EAAE;AACE,MAAC,SAAS,GAAG,GAAG,CAAC;AACtB,EAAE,MAAM,EAAE,EAAE;AACZ,EAAE,QAAQ,EAAE,GAAG,CAAC,2hBAA2hB,CAAC;AAC5iB,EAAE,OAAO,EAAE,GAAG,CAAC,0IAA0I,CAAC;AAC1J,EAAE,KAAK,EAAE,GAAG,CAAC,yBAAyB,CAAC;AACvC,EAAE,aAAa,EAAE,QAAQ;AACzB,EAAE,OAAO,EAAE,EAAE;AACb,EAAE,OAAO,EAAE,GAAG,CAAC,oCAAoC,CAAC;AACpD,EAAE,KAAK,EAAE,EAAE;AACX,CAAC,EAAE;AACE,MAAC,KAAK,GAAG,GAAG,CAAC;AAClB,EAAE,MAAM,EAAE,GAAG,CAAC,gqBAAgqB,CAAC;AAC/qB,EAAE,QAAQ,EAAE,GAAG,CAAC,itDAAitD,CAAC;AACluD,EAAE,OAAO,EAAE,GAAG,CAAC,i9BAAi9B,CAAC;AACj+B,EAAE,aAAa,EAAE,iBAAiB;AAClC,EAAE,OAAO,EAAE,GAAG,CAAC,qBAAqB,CAAC;AACrC,EAAE,OAAO,EAAE,GAAG,CAAC,4DAA4D,CAAC;AAC5E,CAAC,EAAE;AACE,MAAC,IAAI,GAAG,GAAG,CAAC;AACjB,EAAE,QAAQ,EAAE,GAAG,CAAC,k1DAAk1D,CAAC;AACn2D,EAAE,OAAO,EAAE,GAAG,CAAC,wKAAwK,CAAC;AACxL,EAAE,KAAK,EAAE,GAAG,CAAC,yBAAyB,CAAC;AACvC,EAAE,aAAa,EAAE,cAAc;AAC/B,EAAE,OAAO,EAAE,GAAG,CAAC,gBAAgB,CAAC;AAChC,EAAE,OAAO,EAAE,GAAG,CAAC,iDAAiD,CAAC;AACjE,CAAC,EAAE;AACE,MAAC,KAAK,GAAG,GAAG,CAAC;AAClB,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC;AACvB;AACA;AACA,EAAE,QAAQ,EAAE,GAAG,CAAC,WAAW,GAAG,m3NAAm3N,CAAC;AACl5N;AACA,EAAE,OAAO,EAAE,GAAG,CAAC,4ZAA4Z,CAAC;AAC5a,EAAE,KAAK,EAAE,GAAG,CAAC,yBAAyB,CAAC;AACvC,EAAE,aAAa,EAAE,yBAAyB;AAC1C,EAAE,sBAAsB,EAAE,KAAK;AAC/B,EAAE,OAAO,EAAE,GAAG,CAAC,qBAAqB,CAAC;AACrC,EAAE,OAAO,EAAE,GAAG,CAAC,yGAAyG,CAAC;AACzH,CAAC,EAAE;AACE,MAAC,GAAG,GAAG,GAAG,CAAC;AAChB,EAAE,QAAQ,EAAE,GAAG,CAAC,wHAAwH,CAAC;AACzI,EAAE,KAAK,EAAE,GAAG,CAAC,YAAY,CAAC;AAC1B,EAAE,OAAO,EAAE,GAAG,CAAC,oEAAoE,CAAC;AACpF,EAAE,aAAa,EAAE,cAAc;AAC/B,CAAC,EAAE;AACE,MAAC,KAAK,GAAG,GAAG,CAAC;AAClB,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC;AACvB;AACA,EAAE,QAAQ,EAAE,GAAG,CAAC,y7GAAy7G,CAAC;AAC18G,EAAE,OAAO,EAAE,GAAG,CAAC,kaAAka,CAAC;AAClb,EAAE,KAAK,EAAE,GAAG,CAAC,yBAAyB,CAAC;AACvC,EAAE,aAAa,EAAE,uBAAuB;AACxC,EAAE,OAAO,EAAE,GAAG,CAAC,qBAAqB,CAAC;AACrC,EAAE,OAAO,EAAE,GAAG,CAAC,0FAA0F,CAAC;AAC1G,CAAC,EAAE;AACE,MAAC,QAAQ,GAAG,GAAG,CAAC;AACrB,EAAE,QAAQ,EAAE,GAAG,CAAC,i+CAAi+C,CAAC;AACl/C,EAAE,OAAO,EAAE,GAAG,CAAC,mLAAmL,CAAC;AACnM,EAAE,KAAK,EAAE,GAAG,CAAC,iBAAiB,CAAC;AAC/B,EAAE,aAAa,EAAE,oBAAoB;AACrC,EAAE,OAAO,EAAE,GAAG,CAAC,qBAAqB,CAAC;AACrC,EAAE,OAAO,EAAE,GAAG,CAAC,wCAAwC,CAAC;AACxD,CAAC,EAAE;AACE,MAAC,KAAK,GAAG,GAAG,CAAC;AAClB,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,CAAC;AACvB;AACA,EAAE,QAAQ,EAAE,GAAG,CAAC,60BAA60B,CAAC;AAC91B,EAAE,OAAO,EAAE,EAAE;AACb,EAAE,KAAK,EAAE,GAAG,CAAC,iBAAiB,CAAC;AAC/B,EAAE,aAAa,EAAE,uBAAuB;AACxC,EAAE,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC;AACtB,EAAE,OAAO,EAAE,GAAG,CAAC,uDAAuD,CAAC;AACvE,CAAC;;;;"}