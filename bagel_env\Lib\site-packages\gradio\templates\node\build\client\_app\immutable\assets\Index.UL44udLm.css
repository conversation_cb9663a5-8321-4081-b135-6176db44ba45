.gradio-container-5-31-0 Tables */ table,
.gradio-container-5-31-0 tr,
.gradio-container-5-31-0 td,
.gradio-container-5-31-0 th {
	margin-top: var(--spacing-sm);
	margin-bottom: var(--spacing-sm);
	padding: var(--spacing-xl);
}

/* .message-wrap :global(pre[class*="language-"]),
	.message-wrap :global(pre) {
		border: none;
		background: none;
		position: relative;
		direction: ltr;
		white-space: no-wrap;
		overflow-x: auto;
	}
	.message-wrap :global(code) {
	} */

/* .message-wrap :global(div[class*="code_wrap"]) {
		
	} */

.gradio-container-5-31-0 .md code,
.gradio-container-5-31-0 .md pre {
	background: none;
	font-family: var(--font-mono);
	font-size: var(--text-md);

	text-align: left;
	white-space: pre;
	word-spacing: normal;
	word-break: normal;
	word-wrap: normal;
	line-height: 1.5;
	tab-size: 2;

	hyphens: none;
}

.gradio-container-5-31-0 .md pre[class*="language-"]::-moz-selection,
.gradio-container-5-31-0 .md pre[class*="language-"] ::-moz-selection,
.gradio-container-5-31-0 .md code[class*="language-"]::-moz-selection,
.gradio-container-5-31-0 .md code[class*="language-"] ::-moz-selection {
}

.gradio-container-5-31-0 .md pre[class*="language-"]::selection,
.gradio-container-5-31-0 .md pre[class*="language-"] ::selection,
.gradio-container-5-31-0 .md code[class*="language-"]::selection,
.gradio-container-5-31-0 .md code[class*="language-"] ::selection {
	text-shadow: none;
	background: #b3d4fc;
}

/* Code blocks */
.gradio-container-5-31-0 .md pre {
	padding: 1em;
	margin: 0.5em 0;
	overflow: auto;
	position: relative;
	margin-top: var(--spacing-sm);
	margin-bottom: var(--spacing-sm);
	box-shadow: none;
	border: none;
	border-radius: var(--radius-md);
	background: var(--code-background-fill);
	font-family: var(--font-mono);
	display: block;
	white-space: pre;
	border-radius: var(--radius-sm);
	text-shadow: none;
	border-radius: var(--radius-sm);
	/* font-size: 85%; */
	white-space: nowrap;
	display: block;
	white-space: pre;
}

.gradio-container-5-31-0 .prose code {
}
.gradio-container-5-31-0 .prose pre > code {
}

/* Inline code */
.gradio-container-5-31-0 .md :not(pre) > code {
	padding: 0.1em;
	border-radius: var(--radius-xs);
	white-space: normal;
	background: var(--code-background-fill);
	border: 1px solid var(--panel-border-color);
	padding: var(--spacing-xxs) var(--spacing-xs);
}

.gradio-container-5-31-0 .md .token.comment,
.gradio-container-5-31-0 .md .token.prolog,
.gradio-container-5-31-0 .md .token.doctype,
.gradio-container-5-31-0 .md .token.cdata {
	color: slategray;
}

.gradio-container-5-31-0 .md .token.punctuation {
	color: #999;
}

.gradio-container-5-31-0 .md .token.namespace {
	opacity: 0.7;
}

.gradio-container-5-31-0 .md .token.property,
.gradio-container-5-31-0 .md .token.tag,
.gradio-container-5-31-0 .md .token.boolean,
.gradio-container-5-31-0 .md .token.number,
.gradio-container-5-31-0 .md .token.constant,
.gradio-container-5-31-0 .md .token.symbol,
.gradio-container-5-31-0 .md .token.deleted {
	color: #905;
}

.gradio-container-5-31-0 .md .token.selector,
.gradio-container-5-31-0 .md .token.attr-name,
.gradio-container-5-31-0 .md .token.string,
.gradio-container-5-31-0 .md .token.char,
.gradio-container-5-31-0 .md .token.builtin,
.gradio-container-5-31-0 .md .token.inserted {
	color: #690;
}

.gradio-container-5-31-0 .md .token.atrule,
.gradio-container-5-31-0 .md .token.attr-value,
.gradio-container-5-31-0 .md .token.keyword {
	color: #07a;
}

.gradio-container-5-31-0 .md .token.function,
.gradio-container-5-31-0 .md .token.class-name {
	color: #dd4a68;
}

.gradio-container-5-31-0 .md .token.regex,
.gradio-container-5-31-0 .md .token.important,
.gradio-container-5-31-0 .md .token.variable {
	color: #e90;
}

.gradio-container-5-31-0 .md .token.important,
.gradio-container-5-31-0 .md .token.bold {
	font-weight: bold;
}
.gradio-container-5-31-0 .md .token.italic {
	font-style: italic;
}

.gradio-container-5-31-0 .md .token.entity {
	cursor: help;
}

.dark .md .token.comment,
.dark .md .token.prolog,
.dark .md .token.cdata {
	color: hsl(220, 10%, 40%);
}

.dark .md .token.doctype,
.dark .md .token.punctuation,
.dark .md .token.entity {
	color: hsl(220, 14%, 71%);
}

.dark .md .token.attr-name,
.dark .md .token.class-name,
.dark .md .token.boolean,
.dark .md .token.constant,
.dark .md .token.number,
.dark .md .token.atrule {
	color: hsl(29, 54%, 61%);
}

.dark .md .token.keyword {
	color: hsl(286, 60%, 67%);
}

.dark .md .token.property,
.dark .md .token.tag,
.dark .md .token.symbol,
.dark .md .token.deleted,
.dark .md .token.important {
	color: hsl(355, 65%, 65%);
}

.dark .md .token.selector,
.dark .md .token.string,
.dark .md .token.char,
.dark .md .token.builtin,
.dark .md .token.inserted,
.dark .md .token.regex,
.dark .md .token.attr-value,
.dark .md .token.attr-value > .token.punctuation {
	color: hsl(95, 38%, 62%);
}

.dark .md .token.variable,
.dark .md .token.operator,
.dark .md .token.function {
	color: hsl(207, 82%, 66%);
}

.dark .md .token.url {
	color: hsl(187, 47%, 55%);
}
.header.svelte-1mlh4di.svelte-1mlh4di{display:flex;justify-content:space-between;align-items:center;padding:0.7rem 1rem;border-bottom:1px solid var(--table-border-color)}.title.svelte-1mlh4di.svelte-1mlh4di{font-size:var(--scale-0);font-weight:600;color:var(--body-text-color)}.toggle-all.svelte-1mlh4di.svelte-1mlh4di{background:none;border:none;cursor:pointer;padding:0;color:var(--body-text-color);font-size:0.7em;line-height:1;opacity:0.7;transition:opacity 0.2s ease,
			transform 0.3s ease}.toggle-all.svelte-1mlh4di.svelte-1mlh4di:hover{opacity:1}.wrap[data-all-open="true"] .toggle-all.svelte-1mlh4di.svelte-1mlh4di{transform:rotate(180deg)}.default.svelte-1mlh4di pre,.default.svelte-1mlh4di .highlight{display:inline-block}.wrap.svelte-1mlh4di pre,.wrap.svelte-1mlh4di .highlight{margin:0 !important;background:transparent !important;font-family:var(--font-mono);font-weight:400;padding:0 !important}.wrap.svelte-1mlh4di pre a{color:var(--link-text-color-hover);text-decoration:underline}.wrap.svelte-1mlh4di pre a:hover{color:var(--link-text-color-hover)}.default.svelte-1mlh4di>span.svelte-1mlh4di{text-transform:uppercase;font-size:0.7rem;font-weight:600}.default.svelte-1mlh4di>code.svelte-1mlh4di{border:none}code.svelte-1mlh4di.svelte-1mlh4di{background:none;font-family:var(--font-mono)}.wrap.svelte-1mlh4di.svelte-1mlh4di{padding:0rem;border-radius:5px;border:1px solid #eee;overflow:hidden;position:relative;margin:0;box-shadow:var(--block-shadow);border-width:var(--block-border-width);border-color:var(--block-border-color);border-radius:var(--block-radius);width:100%;line-height:var(--line-sm);color:var(--body-text-color)}.type.svelte-1mlh4di.svelte-1mlh4di{position:relative;padding:0.7rem 1rem;padding-left:2rem;background:var(--table-odd-background-fill);border-bottom:0px solid var(--table-border-color);list-style:none}.type.svelte-1mlh4di.svelte-1mlh4di::after{content:"▼";position:absolute;top:50%;right:15px;transform:translateY(-50%);transition:transform 0.3s ease;font-size:0.7em;opacity:0.7}details[open].svelte-1mlh4di .type.svelte-1mlh4di::after{transform:translateY(-50%) rotate(180deg)}.default.svelte-1mlh4di.svelte-1mlh4di{padding:0.2rem 1rem 0.3rem 1rem;border-bottom:1px solid var(--table-border-color);background:var(--block-background-fill)}.default.last.svelte-1mlh4di.svelte-1mlh4di{border-bottom:none}.description.svelte-1mlh4di.svelte-1mlh4di{padding:0.7rem 1rem;font-size:var(--scale-00);font-family:var(--font-sans);background:var(--block-background-fill)}.param.svelte-1mlh4di.svelte-1mlh4di{border-bottom:1px solid var(--table-border-color)}.param.svelte-1mlh4di.svelte-1mlh4di:last-child{border-bottom:none}details[open].svelte-1mlh4di .type.svelte-1mlh4di{border-bottom-width:1px}.param.md.svelte-1mlh4di code.svelte-1mlh4di{background:none}details.svelte-1mlh4di>summary.svelte-1mlh4di{cursor:pointer}details.svelte-1mlh4di>summary.svelte-1mlh4di::-webkit-details-marker{display:none}.param-link.svelte-1mlh4di.svelte-1mlh4di{opacity:0;position:absolute;left:8px;top:50%;transform:translateY(-50%);transition:opacity 0.2s;color:var(--body-text-color);text-decoration:none}.link-icon.svelte-1mlh4di.svelte-1mlh4di{font-size:14px}.type.svelte-1mlh4di:hover .param-link.svelte-1mlh4di{opacity:0.7}.param-link.svelte-1mlh4di.svelte-1mlh4di:hover{opacity:1 !important}