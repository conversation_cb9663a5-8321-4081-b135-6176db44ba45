{"version": 3, "file": "index4-BIAFQWR9.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/index4.js"], "sourcesContent": ["function json(data, init) {\n  const body = JSON.stringify(data);\n  const headers = new Headers(init?.headers);\n  if (!headers.has(\"content-length\")) {\n    headers.set(\"content-length\", encoder.encode(body).byteLength.toString());\n  }\n  if (!headers.has(\"content-type\")) {\n    headers.set(\"content-type\", \"application/json\");\n  }\n  return new Response(body, {\n    ...init,\n    headers\n  });\n}\nconst encoder = new TextEncoder();\nfunction text(body, init) {\n  const headers = new Headers(init?.headers);\n  if (!headers.has(\"content-length\")) {\n    const encoded = encoder.encode(body);\n    headers.set(\"content-length\", encoded.byteLength.toString());\n    return new Response(encoded, {\n      ...init,\n      headers\n    });\n  }\n  return new Response(body, {\n    ...init,\n    headers\n  });\n}\nexport {\n  json as j,\n  text as t\n};\n"], "names": [], "mappings": "AAAA,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACpC,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACtC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9E,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AACpC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,GAAG,IAAI;AACX,IAAI,OAAO;AACX,GAAG,CAAC,CAAC;AACL,CAAC;AACD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;AAClC,SAAS,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,EAAE,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE;AACtC,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;AACjE,IAAI,OAAO,IAAI,QAAQ,CAAC,OAAO,EAAE;AACjC,MAAM,GAAG,IAAI;AACb,MAAM,OAAO;AACb,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE;AAC5B,IAAI,GAAG,IAAI;AACX,IAAI,OAAO;AACX,GAAG,CAAC,CAAC;AACL;;;;"}