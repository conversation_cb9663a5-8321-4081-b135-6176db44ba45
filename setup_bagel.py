#!/usr/bin/env python3
"""
Setup script for BAGEL project
This script helps set up the BAGEL environment and download the model
"""

import os
import sys
import subprocess
import platform

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n🔄 {description}...")
    try:
        if platform.system() == "Windows":
            # For Windows, use shell=True
            result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        else:
            # For Unix-like systems
            result = subprocess.run(command.split(), check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed!")
        print(f"Error: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major == 3 and version.minor >= 10:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} is not compatible")
        print("Please install Python 3.10 or higher")
        return False

def check_virtual_environment():
    """Check if we're in a virtual environment"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment detected")
        return True
    else:
        print("⚠️  No virtual environment detected")
        print("It's recommended to use a virtual environment")
        return False

def install_requirements():
    """Install Python requirements"""
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"❌ {requirements_file} not found!")
        return False
    
    # Install requirements
    command = f"pip install -r {requirements_file}"
    return run_command(command, "Installing Python requirements")

def install_gradio():
    """Install Gradio for the web interface"""
    command = "pip install gradio"
    return run_command(command, "Installing Gradio")

def download_model():
    """Download the BAGEL model"""
    print("\n🔄 Downloading BAGEL model...")
    print("This may take a while (several GB download)...")
    
    try:
        # Import and run the download function
        from download_model import download_bagel_model
        success = download_bagel_model()
        if success:
            print("✅ Model download completed!")
        else:
            print("❌ Model download failed!")
        return success
    except Exception as e:
        print(f"❌ Error downloading model: {e}")
        return False

def main():
    """Main setup function"""
    print("🥯 BAGEL Setup Script")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check virtual environment
    check_virtual_environment()
    
    # Install requirements
    print("\n📦 Installing dependencies...")
    if not install_gradio():
        print("❌ Failed to install Gradio")
        sys.exit(1)
    
    # Note about flash_attn
    print("\n⚠️  Note: flash_attn is not installed (requires CUDA compilation)")
    print("The project should work without it, but performance may be affected")
    
    # Download model
    print("\n📥 Model Download")
    download_choice = input("Do you want to download the BAGEL model now? (y/n): ").lower().strip()
    
    if download_choice in ['y', 'yes']:
        if not download_model():
            print("❌ Model download failed. You can try again later using:")
            print("python download_model.py")
    else:
        print("⏭️  Skipping model download. You can download it later using:")
        print("python download_model.py")
    
    print("\n🎉 Setup completed!")
    print("\n📋 Next steps:")
    print("1. If you haven't downloaded the model yet, run: python download_model.py")
    print("2. Start the web interface: python app.py")
    print("3. Open your browser and go to: http://127.0.0.1:7860")
    
    print("\n💡 Tips:")
    print("- The model requires significant GPU memory for optimal performance")
    print("- CPU-only mode is supported but will be slower")
    print("- Check the README.md for more detailed instructions")

if __name__ == "__main__":
    main()
