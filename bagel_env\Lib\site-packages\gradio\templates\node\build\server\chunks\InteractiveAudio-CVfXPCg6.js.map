{"version": 3, "file": "InteractiveAudio-CVfXPCg6.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/InteractiveAudio.js"], "sourcesContent": ["import { create_ssr_component, escape, each, add_attribute, validate_component, add_styles, null_to_empty } from \"svelte/internal\";\nimport { createEventDispatcher, onMount, onDestroy } from \"svelte\";\nimport { M as ModifyUpload, U as Upload } from \"./ModifyUpload.js\";\nimport { a9 as Pause, a2 as Spinner, B as BlockLabel, M as Music, f as StreamingBar, W as SelectSource, Y as prepare_files } from \"./client.js\";\nimport { s as skip_audio, A as AudioPlayer } from \"./AudioPlayer.js\";\nfunction e(e2, t2, i2, s2) {\n  return new (i2 || (i2 = Promise))(function(r2, n) {\n    function o(e3) {\n      try {\n        d(s2.next(e3));\n      } catch (e4) {\n        n(e4);\n      }\n    }\n    function a(e3) {\n      try {\n        d(s2.throw(e3));\n      } catch (e4) {\n        n(e4);\n      }\n    }\n    function d(e3) {\n      var t3;\n      e3.done ? r2(e3.value) : (t3 = e3.value, t3 instanceof i2 ? t3 : new i2(function(e4) {\n        e4(t3);\n      })).then(o, a);\n    }\n    d((s2 = s2.apply(e2, [])).next());\n  });\n}\n\"function\" == typeof SuppressedError && SuppressedError;\nclass t {\n  constructor() {\n    this.listeners = {}, this.on = this.addEventListener, this.un = this.removeEventListener;\n  }\n  addEventListener(e2, t2, i2) {\n    if (this.listeners[e2] || (this.listeners[e2] = /* @__PURE__ */ new Set()), this.listeners[e2].add(t2), null == i2 ? void 0 : i2.once) {\n      const i3 = () => {\n        this.removeEventListener(e2, i3), this.removeEventListener(e2, t2);\n      };\n      return this.addEventListener(e2, i3), i3;\n    }\n    return () => this.removeEventListener(e2, t2);\n  }\n  removeEventListener(e2, t2) {\n    var i2;\n    null === (i2 = this.listeners[e2]) || void 0 === i2 || i2.delete(t2);\n  }\n  once(e2, t2) {\n    return this.on(e2, t2, { once: true });\n  }\n  unAll() {\n    this.listeners = {};\n  }\n  emit(e2, ...t2) {\n    this.listeners[e2] && this.listeners[e2].forEach((e3) => e3(...t2));\n  }\n}\nclass i extends t {\n  constructor(e2) {\n    super(), this.subscriptions = [], this.options = e2;\n  }\n  onInit() {\n  }\n  init(e2) {\n    this.wavesurfer = e2, this.onInit();\n  }\n  destroy() {\n    this.emit(\"destroy\"), this.subscriptions.forEach((e2) => e2());\n  }\n}\nconst s = [\"audio/webm\", \"audio/wav\", \"audio/mpeg\", \"audio/mp4\", \"audio/mp3\"];\nclass r extends i {\n  constructor(e2) {\n    var t2;\n    super(Object.assign(Object.assign({}, e2), { audioBitsPerSecond: null !== (t2 = e2.audioBitsPerSecond) && void 0 !== t2 ? t2 : 128e3 })), this.stream = null, this.mediaRecorder = null;\n  }\n  static create(e2) {\n    return new r(e2 || {});\n  }\n  renderMicStream(e2) {\n    const t2 = new AudioContext(), i2 = t2.createMediaStreamSource(e2), s2 = t2.createAnalyser();\n    i2.connect(s2);\n    const r2 = s2.frequencyBinCount, n = new Float32Array(r2), o = r2 / t2.sampleRate;\n    let a;\n    const d = () => {\n      s2.getFloatTimeDomainData(n), this.wavesurfer && (this.wavesurfer.options.cursorWidth = 0, this.wavesurfer.options.interact = false, this.wavesurfer.load(\"\", [n], o)), a = requestAnimationFrame(d);\n    };\n    return d(), () => {\n      cancelAnimationFrame(a), null == i2 || i2.disconnect(), null == t2 || t2.close();\n    };\n  }\n  startMic(t2) {\n    return e(this, void 0, void 0, function* () {\n      let e2;\n      try {\n        e2 = yield navigator.mediaDevices.getUserMedia({ audio: !(null == t2 ? void 0 : t2.deviceId) || { deviceId: t2.deviceId } });\n      } catch (e3) {\n        throw new Error(\"Error accessing the microphone: \" + e3.message);\n      }\n      const i2 = this.renderMicStream(e2);\n      return this.subscriptions.push(this.once(\"destroy\", i2)), this.stream = e2, e2;\n    });\n  }\n  stopMic() {\n    this.stream && (this.stream.getTracks().forEach((e2) => e2.stop()), this.stream = null, this.mediaRecorder = null);\n  }\n  startRecording(t2) {\n    return e(this, void 0, void 0, function* () {\n      const e2 = this.stream || (yield this.startMic(t2)), i2 = this.mediaRecorder || new MediaRecorder(e2, { mimeType: this.options.mimeType || s.find((e3) => MediaRecorder.isTypeSupported(e3)), audioBitsPerSecond: this.options.audioBitsPerSecond });\n      this.mediaRecorder = i2, this.stopRecording();\n      const r2 = [];\n      i2.ondataavailable = (e3) => {\n        e3.data.size > 0 && r2.push(e3.data);\n      }, i2.onstop = () => {\n        var e3;\n        const t3 = new Blob(r2, { type: i2.mimeType });\n        this.emit(\"record-end\", t3), false !== this.options.renderRecordedAudio && (null === (e3 = this.wavesurfer) || void 0 === e3 || e3.load(URL.createObjectURL(t3)));\n      }, i2.start(), this.emit(\"record-start\");\n    });\n  }\n  isRecording() {\n    var e2;\n    return \"recording\" === (null === (e2 = this.mediaRecorder) || void 0 === e2 ? void 0 : e2.state);\n  }\n  isPaused() {\n    var e2;\n    return \"paused\" === (null === (e2 = this.mediaRecorder) || void 0 === e2 ? void 0 : e2.state);\n  }\n  stopRecording() {\n    var e2;\n    this.isRecording() && (null === (e2 = this.mediaRecorder) || void 0 === e2 || e2.stop());\n  }\n  pauseRecording() {\n    var e2;\n    this.isRecording() && (null === (e2 = this.mediaRecorder) || void 0 === e2 || e2.pause(), this.emit(\"record-pause\"));\n  }\n  resumeRecording() {\n    var e2;\n    this.isPaused() && (null === (e2 = this.mediaRecorder) || void 0 === e2 || e2.resume(), this.emit(\"record-resume\"));\n  }\n  static getAvailableAudioDevices() {\n    return e(this, void 0, void 0, function* () {\n      return navigator.mediaDevices.enumerateDevices().then((e2) => e2.filter((e3) => \"audioinput\" === e3.kind));\n    });\n  }\n  destroy() {\n    super.destroy(), this.stopRecording(), this.stopMic();\n  }\n}\nconst css$4 = {\n  code: \".mic-select.svelte-1ya9x7a{height:var(--size-8);background:var(--block-background-fill);padding:0px var(--spacing-xxl);border-radius:var(--button-large-radius);font-size:var(--text-md);border:1px solid var(--block-border-color);gap:var(--size-1)}select.svelte-1ya9x7a{text-overflow:ellipsis;max-width:var(--size-40)}@media(max-width: 375px){select.svelte-1ya9x7a{width:100%}}\",\n  map: '{\"version\":3,\"file\":\"DeviceSelect.svelte\",\"sources\":[\"DeviceSelect.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import RecordPlugin from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nexport let i18n;\\\\nexport let micDevices = [];\\\\nconst dispatch = createEventDispatcher();\\\\n$: if (typeof window !== \\\\\"undefined\\\\\") {\\\\n    try {\\\\n        let tempDevices = [];\\\\n        RecordPlugin.getAvailableAudioDevices().then((devices) => {\\\\n            micDevices = devices;\\\\n            devices.forEach((device) => {\\\\n                if (device.deviceId) {\\\\n                    tempDevices.push(device);\\\\n                }\\\\n            });\\\\n            micDevices = tempDevices;\\\\n        });\\\\n    }\\\\n    catch (err) {\\\\n        if (err instanceof DOMException && err.name == \\\\\"NotAllowedError\\\\\") {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"audio.allow_recording_access\\\\\"));\\\\n        }\\\\n        throw err;\\\\n    }\\\\n}\\\\n<\\/script>\\\\n\\\\n<select\\\\n\\\\tclass=\\\\\"mic-select\\\\\"\\\\n\\\\taria-label=\\\\\"Select input device\\\\\"\\\\n\\\\tdisabled={micDevices.length === 0}\\\\n>\\\\n\\\\t{#if micDevices.length === 0}\\\\n\\\\t\\\\t<option value=\\\\\"\\\\\">{i18n(\\\\\"audio.no_microphone\\\\\")}</option>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t{#each micDevices as micDevice}\\\\n\\\\t\\\\t\\\\t<option value={micDevice.deviceId}>{micDevice.label}</option>\\\\n\\\\t\\\\t{/each}\\\\n\\\\t{/if}\\\\n</select>\\\\n\\\\n<style>\\\\n\\\\t.mic-select {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tbackground: var(--block-background-fill);\\\\n\\\\t\\\\tpadding: 0px var(--spacing-xxl);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tgap: var(--size-1);\\\\n\\\\t}\\\\n\\\\n\\\\tselect {\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\tmax-width: var(--size-40);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (max-width: 375px) {\\\\n\\\\t\\\\tselect {\\\\n\\\\t\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA0CC,0BAAY,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,OAAO,CAAE,GAAG,CAAC,IAAI,aAAa,CAAC,CAC/B,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,GAAG,CAAE,IAAI,QAAQ,CAClB,CAEA,qBAAO,CACN,aAAa,CAAE,QAAQ,CACvB,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,qBAAO,CACN,KAAK,CAAE,IACR,CACD\"}'\n};\nconst DeviceSelect = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { i18n } = $$props;\n  let { micDevices = [] } = $$props;\n  const dispatch = createEventDispatcher();\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.micDevices === void 0 && $$bindings.micDevices && micDevices !== void 0)\n    $$bindings.micDevices(micDevices);\n  $$result.css.add(css$4);\n  {\n    if (typeof window !== \"undefined\") {\n      try {\n        let tempDevices = [];\n        r.getAvailableAudioDevices().then((devices) => {\n          micDevices = devices;\n          devices.forEach((device) => {\n            if (device.deviceId) {\n              tempDevices.push(device);\n            }\n          });\n          micDevices = tempDevices;\n        });\n      } catch (err) {\n        if (err instanceof DOMException && err.name == \"NotAllowedError\") {\n          dispatch(\"error\", i18n(\"audio.allow_recording_access\"));\n        }\n        throw err;\n      }\n    }\n  }\n  return `<select class=\"mic-select svelte-1ya9x7a\" aria-label=\"Select input device\" ${micDevices.length === 0 ? \"disabled\" : \"\"}>${micDevices.length === 0 ? `<option value=\"\">${escape(i18n(\"audio.no_microphone\"))}</option>` : `${each(micDevices, (micDevice) => {\n    return `<option${add_attribute(\"value\", micDevice.deviceId, 0)}>${escape(micDevice.label)}</option>`;\n  })}`}</select>`;\n});\nconst css$3 = {\n  code: '.controls.svelte-1oiuk2f{display:flex;align-items:center;justify-content:space-between;flex-wrap:wrap}.wrapper.svelte-1oiuk2f{display:flex;align-items:center;flex-wrap:wrap}.record.svelte-1oiuk2f{margin-right:var(--spacing-md)}.stop-button-paused.svelte-1oiuk2f{display:none;height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--block-border-color);margin:var(--size-1) var(--size-1) 0 0}.stop-button-paused.svelte-1oiuk2f::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.stop-button.svelte-1oiuk2f::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl);animation:svelte-1oiuk2f-scaling 1800ms infinite}.stop-button.svelte-1oiuk2f{display:none;height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--primary-600);margin:var(--size-1) var(--size-1) 0 0}.record-button.svelte-1oiuk2f::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.record-button.svelte-1oiuk2f{height:var(--size-8);width:var(--size-24);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);display:flex;align-items:center;border:1px solid var(--block-border-color)}.duration-button.svelte-1oiuk2f{border-radius:var(--button-large-radius)}.stop-button.svelte-1oiuk2f:disabled{cursor:not-allowed}.record-button.svelte-1oiuk2f:disabled{cursor:not-allowed;opacity:0.5}@keyframes svelte-1oiuk2f-scaling{0%{background-color:var(--primary-600);scale:1}50%{background-color:var(--primary-600);scale:1.2}100%{background-color:var(--primary-600);scale:1}}.pause-button.svelte-1oiuk2f{display:none;height:var(--size-8);width:var(--size-20);border:1px solid var(--block-border-color);border-radius:var(--button-large-radius);padding:var(--spacing-md);margin:var(--size-1) var(--size-1) 0 0}.resume-button.svelte-1oiuk2f{display:none;height:var(--size-8);width:var(--size-20);border:1px solid var(--block-border-color);border-radius:var(--button-large-radius);padding:var(--spacing-xl);line-height:1px;font-size:var(--text-md);margin:var(--size-1) var(--size-1) 0 0}.duration.svelte-1oiuk2f{display:flex;height:var(--size-8);width:var(--size-20);border:1px solid var(--block-border-color);padding:var(--spacing-md);align-items:center;justify-content:center;margin:var(--size-1) var(--size-1) 0 0}::part(region){border-radius:var(--radius-md);height:98% !important;border:1px solid var(--trim-region-color);background-color:unset;border-width:1px 3px}::part(region)::after{content:\"\";position:absolute;top:0;left:0;width:100%;height:100%;background:var(--trim-region-color);opacity:0.2;border-radius:var(--radius-md)}::part(region-handle){width:5px !important;border:none}',\n  map: `{\"version\":3,\"file\":\"WaveformRecordControls.svelte\",\"sources\":[\"WaveformRecordControls.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Pause } from \\\\\"@gradio/icons\\\\\";\\\\nimport RecordPlugin from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport DeviceSelect from \\\\\"./DeviceSelect.svelte\\\\\";\\\\nexport let record;\\\\nexport let i18n;\\\\nexport let recording = false;\\\\nlet micDevices = [];\\\\nlet recordButton;\\\\nlet pauseButton;\\\\nlet resumeButton;\\\\nlet stopButton;\\\\nlet stopButtonPaused;\\\\nlet recording_ongoing = false;\\\\nexport let record_time;\\\\nexport let show_recording_waveform;\\\\nexport let timing = false;\\\\n$: record.on(\\\\\"record-start\\\\\", () => {\\\\n    record.startMic();\\\\n    recordButton.style.display = \\\\\"none\\\\\";\\\\n    stopButton.style.display = \\\\\"flex\\\\\";\\\\n    pauseButton.style.display = \\\\\"block\\\\\";\\\\n});\\\\n$: record.on(\\\\\"record-end\\\\\", () => {\\\\n    if (record.isPaused()) {\\\\n        record.resumeRecording();\\\\n        record.stopRecording();\\\\n    }\\\\n    record.stopMic();\\\\n    recordButton.style.display = \\\\\"flex\\\\\";\\\\n    stopButton.style.display = \\\\\"none\\\\\";\\\\n    pauseButton.style.display = \\\\\"none\\\\\";\\\\n    recordButton.disabled = false;\\\\n});\\\\n$: record.on(\\\\\"record-pause\\\\\", () => {\\\\n    pauseButton.style.display = \\\\\"none\\\\\";\\\\n    resumeButton.style.display = \\\\\"block\\\\\";\\\\n    stopButton.style.display = \\\\\"none\\\\\";\\\\n    stopButtonPaused.style.display = \\\\\"flex\\\\\";\\\\n});\\\\n$: record.on(\\\\\"record-resume\\\\\", () => {\\\\n    pauseButton.style.display = \\\\\"block\\\\\";\\\\n    resumeButton.style.display = \\\\\"none\\\\\";\\\\n    recordButton.style.display = \\\\\"none\\\\\";\\\\n    stopButton.style.display = \\\\\"flex\\\\\";\\\\n    stopButtonPaused.style.display = \\\\\"none\\\\\";\\\\n});\\\\n$: if (recording && !recording_ongoing) {\\\\n    record.startRecording();\\\\n    recording_ongoing = true;\\\\n}\\\\nelse {\\\\n    record.stopRecording();\\\\n    recording_ongoing = false;\\\\n}\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"controls\\\\\">\\\\n\\\\t<div class=\\\\\"wrapper\\\\\">\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tbind:this={recordButton}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"record record-button\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => record.startRecording()}>{i18n(\\\\\"audio.record\\\\\")}</button\\\\n\\\\t\\\\t>\\\\n\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tbind:this={stopButton}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"stop-button {record.isPaused() ? 'stop-button-paused' : ''}\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\tif (record.isPaused()) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord.resumeRecording();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord.stopRecording();\\\\n\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t\\\\t\\\\trecord.stopRecording();\\\\n\\\\t\\\\t\\\\t}}>{i18n(\\\\\"audio.stop\\\\\")}</button\\\\n\\\\t\\\\t>\\\\n\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tbind:this={stopButtonPaused}\\\\n\\\\t\\\\t\\\\tid=\\\\\"stop-paused\\\\\"\\\\n\\\\t\\\\t\\\\tclass=\\\\\"stop-button-paused\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\tif (record.isPaused()) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord.resumeRecording();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord.stopRecording();\\\\n\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t\\\\t\\\\trecord.stopRecording();\\\\n\\\\t\\\\t\\\\t}}>{i18n(\\\\\"audio.stop\\\\\")}</button\\\\n\\\\t\\\\t>\\\\n\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\taria-label=\\\\\"pause\\\\\"\\\\n\\\\t\\\\t\\\\tbind:this={pauseButton}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"pause-button\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => record.pauseRecording()}><Pause /></button\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\tbind:this={resumeButton}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"resume-button\\\\\"\\\\n\\\\t\\\\t\\\\ton:click={() => record.resumeRecording()}>{i18n(\\\\\"audio.resume\\\\\")}</button\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t{#if timing && !show_recording_waveform}\\\\n\\\\t\\\\t\\\\t<time class=\\\\\"duration-button duration\\\\\">{record_time}</time>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n\\\\t<DeviceSelect bind:micDevices {i18n} />\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.controls {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.record {\\\\n\\\\t\\\\tmargin-right: var(--spacing-md);\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button-paused {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button-paused::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\t.stop-button::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t\\\\tanimation: scaling 1800ms infinite;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--primary-600);\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-24);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.duration-button {\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button:disabled {\\\\n\\\\t\\\\tcursor: not-allowed;\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button:disabled {\\\\n\\\\t\\\\tcursor: not-allowed;\\\\n\\\\t\\\\topacity: 0.5;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes scaling {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1.2;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.pause-button {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tpadding: var(--spacing-md);\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.resume-button {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tpadding: var(--spacing-xl);\\\\n\\\\t\\\\tline-height: 1px;\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.duration {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tpadding: var(--spacing-md);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tmargin: var(--size-1) var(--size-1) 0 0;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(::part(region)) {\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t\\\\theight: 98% !important;\\\\n\\\\t\\\\tborder: 1px solid var(--trim-region-color);\\\\n\\\\t\\\\tbackground-color: unset;\\\\n\\\\t\\\\tborder-width: 1px 3px;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(::part(region))::after {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tbackground: var(--trim-region-color);\\\\n\\\\t\\\\topacity: 0.2;\\\\n\\\\t\\\\tborder-radius: var(--radius-md);\\\\n\\\\t}\\\\n\\\\n\\\\t:global(::part(region-handle)) {\\\\n\\\\t\\\\twidth: 5px !important;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8GC,wBAAU,CACT,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,aAAa,CAC9B,SAAS,CAAE,IACZ,CAEA,uBAAS,CACR,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,SAAS,CAAE,IACZ,CAEA,sBAAQ,CACP,YAAY,CAAE,IAAI,YAAY,CAC/B,CAEA,kCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,kCAAmB,QAAS,CAC3B,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CACA,2BAAY,QAAS,CACpB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,SAAS,CAAE,sBAAO,CAAC,MAAM,CAAC,QAC3B,CAEA,2BAAa,CACZ,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,6BAAc,QAAS,CACtB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CAEA,6BAAe,CACd,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAC3C,CAEA,+BAAiB,CAChB,aAAa,CAAE,IAAI,qBAAqB,CACzC,CAEA,2BAAY,SAAU,CACrB,MAAM,CAAE,WACT,CAEA,6BAAc,SAAU,CACvB,MAAM,CAAE,WAAW,CACnB,OAAO,CAAE,GACV,CAEA,WAAW,sBAAQ,CAClB,EAAG,CACF,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACA,GAAI,CACH,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,GACR,CACA,IAAK,CACJ,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACD,CAEA,4BAAc,CACb,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,6BAAe,CACd,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,WAAW,CAAE,GAAG,CAChB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEA,wBAAU,CACT,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,OAAO,CAAE,IAAI,YAAY,CAAC,CAC1B,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,MAAM,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CACvC,CAEQ,cAAgB,CACvB,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,MAAM,CAAE,GAAG,CAAC,UAAU,CACtB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAC1C,gBAAgB,CAAE,KAAK,CACvB,YAAY,CAAE,GAAG,CAAC,GACnB,CAEQ,cAAe,OAAQ,CAC9B,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,IAAI,mBAAmB,CAAC,CACpC,OAAO,CAAE,GAAG,CACZ,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEQ,qBAAuB,CAC9B,KAAK,CAAE,GAAG,CAAC,UAAU,CACrB,MAAM,CAAE,IACT\"}`\n};\ncreate_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { record } = $$props;\n  let { i18n } = $$props;\n  let { recording = false } = $$props;\n  let micDevices = [];\n  let recordButton;\n  let pauseButton;\n  let resumeButton;\n  let stopButton;\n  let stopButtonPaused;\n  let recording_ongoing = false;\n  let { record_time } = $$props;\n  let { show_recording_waveform } = $$props;\n  let { timing = false } = $$props;\n  if ($$props.record === void 0 && $$bindings.record && record !== void 0)\n    $$bindings.record(record);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.recording === void 0 && $$bindings.recording && recording !== void 0)\n    $$bindings.recording(recording);\n  if ($$props.record_time === void 0 && $$bindings.record_time && record_time !== void 0)\n    $$bindings.record_time(record_time);\n  if ($$props.show_recording_waveform === void 0 && $$bindings.show_recording_waveform && show_recording_waveform !== void 0)\n    $$bindings.show_recording_waveform(show_recording_waveform);\n  if ($$props.timing === void 0 && $$bindings.timing && timing !== void 0)\n    $$bindings.timing(timing);\n  $$result.css.add(css$3);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      record.on(\"record-start\", () => {\n        record.startMic();\n        recordButton.style.display = \"none\";\n        stopButton.style.display = \"flex\";\n        pauseButton.style.display = \"block\";\n      });\n    }\n    {\n      record.on(\"record-end\", () => {\n        if (record.isPaused()) {\n          record.resumeRecording();\n          record.stopRecording();\n        }\n        record.stopMic();\n        recordButton.style.display = \"flex\";\n        stopButton.style.display = \"none\";\n        pauseButton.style.display = \"none\";\n        recordButton.disabled = false;\n      });\n    }\n    {\n      record.on(\"record-pause\", () => {\n        pauseButton.style.display = \"none\";\n        resumeButton.style.display = \"block\";\n        stopButton.style.display = \"none\";\n        stopButtonPaused.style.display = \"flex\";\n      });\n    }\n    {\n      record.on(\"record-resume\", () => {\n        pauseButton.style.display = \"block\";\n        resumeButton.style.display = \"none\";\n        recordButton.style.display = \"none\";\n        stopButton.style.display = \"flex\";\n        stopButtonPaused.style.display = \"none\";\n      });\n    }\n    {\n      if (recording && !recording_ongoing) {\n        record.startRecording();\n        recording_ongoing = true;\n      } else {\n        record.stopRecording();\n        recording_ongoing = false;\n      }\n    }\n    $$rendered = `<div class=\"controls svelte-1oiuk2f\"><div class=\"wrapper svelte-1oiuk2f\"><button class=\"record record-button svelte-1oiuk2f\"${add_attribute(\"this\", recordButton, 0)}>${escape(i18n(\"audio.record\"))}</button> <button class=\"${\"stop-button \" + escape(record.isPaused() ? \"stop-button-paused\" : \"\", true) + \" svelte-1oiuk2f\"}\"${add_attribute(\"this\", stopButton, 0)}>${escape(i18n(\"audio.stop\"))}</button> <button id=\"stop-paused\" class=\"stop-button-paused svelte-1oiuk2f\"${add_attribute(\"this\", stopButtonPaused, 0)}>${escape(i18n(\"audio.stop\"))}</button> <button aria-label=\"pause\" class=\"pause-button svelte-1oiuk2f\"${add_attribute(\"this\", pauseButton, 0)}>${validate_component(Pause, \"Pause\").$$render($$result, {}, {}, {})}</button> <button class=\"resume-button svelte-1oiuk2f\"${add_attribute(\"this\", resumeButton, 0)}>${escape(i18n(\"audio.resume\"))}</button> ${timing && !show_recording_waveform ? `<time class=\"duration-button duration svelte-1oiuk2f\">${escape(record_time)}</time>` : ``}</div> ${validate_component(DeviceSelect, \"DeviceSelect\").$$render(\n      $$result,\n      { i18n, micDevices },\n      {\n        micDevices: ($$value) => {\n          micDevices = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )} </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst css$2 = {\n  code: \".microphone.svelte-9n45fh{width:100%;display:none}.component-wrapper.svelte-9n45fh{padding:var(--size-3);width:100%}.timestamps.svelte-9n45fh{display:flex;justify-content:space-between;align-items:center;width:100%;padding:var(--size-1) 0;margin:var(--spacing-md) 0}.time.svelte-9n45fh{color:var(--neutral-400)}.duration.svelte-9n45fh{color:var(--neutral-400)}.trim-duration.svelte-9n45fh{color:var(--color-accent);margin-right:var(--spacing-sm)}\",\n  map: '{\"version\":3,\"file\":\"AudioRecorder.svelte\",\"sources\":[\"AudioRecorder.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nimport { skip_audio, process_audio } from \\\\\"../shared/utils\\\\\";\\\\nimport WSRecord from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport WaveformControls from \\\\\"../shared/WaveformControls.svelte\\\\\";\\\\nimport WaveformRecordControls from \\\\\"../shared/WaveformRecordControls.svelte\\\\\";\\\\nimport RecordPlugin from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport { format_time } from \\\\\"@gradio/utils\\\\\";\\\\nexport let mode;\\\\nexport let i18n;\\\\nexport let dispatch_blob;\\\\nexport let waveform_settings;\\\\nexport let waveform_options = {\\\\n    show_recording_waveform: true\\\\n};\\\\nexport let handle_reset_value;\\\\nexport let editable = true;\\\\nexport let recording = false;\\\\nlet micWaveform;\\\\nlet recordingWaveform;\\\\nlet playing = false;\\\\nlet recordingContainer;\\\\nlet microphoneContainer;\\\\nlet record;\\\\nlet recordedAudio = null;\\\\nlet timeRef;\\\\nlet durationRef;\\\\nlet audio_duration;\\\\nlet seconds = 0;\\\\nlet interval;\\\\nlet timing = false;\\\\nlet trimDuration = 0;\\\\nconst start_interval = () => {\\\\n    clearInterval(interval);\\\\n    interval = setInterval(() => {\\\\n        seconds++;\\\\n    }, 1e3);\\\\n};\\\\nconst dispatch = createEventDispatcher();\\\\nfunction record_start_callback() {\\\\n    start_interval();\\\\n    timing = true;\\\\n    dispatch(\\\\\"start_recording\\\\\");\\\\n    if (waveform_options.show_recording_waveform) {\\\\n        let waveformCanvas = microphoneContainer;\\\\n        if (waveformCanvas)\\\\n            waveformCanvas.style.display = \\\\\"block\\\\\";\\\\n    }\\\\n}\\\\nasync function record_end_callback(blob) {\\\\n    seconds = 0;\\\\n    timing = false;\\\\n    clearInterval(interval);\\\\n    try {\\\\n        const array_buffer = await blob.arrayBuffer();\\\\n        const context = new AudioContext({\\\\n            sampleRate: waveform_settings.sampleRate\\\\n        });\\\\n        const audio_buffer = await context.decodeAudioData(array_buffer);\\\\n        if (audio_buffer)\\\\n            await process_audio(audio_buffer).then(async (audio) => {\\\\n                await dispatch_blob([audio], \\\\\"change\\\\\");\\\\n                await dispatch_blob([audio], \\\\\"stop_recording\\\\\");\\\\n            });\\\\n    }\\\\n    catch (e) {\\\\n        console.error(e);\\\\n    }\\\\n}\\\\n$: record?.on(\\\\\"record-resume\\\\\", () => {\\\\n    start_interval();\\\\n});\\\\n$: recordingWaveform?.on(\\\\\"decode\\\\\", (duration) => {\\\\n    audio_duration = duration;\\\\n    durationRef && (durationRef.textContent = format_time(duration));\\\\n});\\\\n$: recordingWaveform?.on(\\\\\"timeupdate\\\\\", (currentTime) => timeRef && (timeRef.textContent = format_time(currentTime)));\\\\n$: recordingWaveform?.on(\\\\\"pause\\\\\", () => {\\\\n    dispatch(\\\\\"pause\\\\\");\\\\n    playing = false;\\\\n});\\\\n$: recordingWaveform?.on(\\\\\"play\\\\\", () => {\\\\n    dispatch(\\\\\"play\\\\\");\\\\n    playing = true;\\\\n});\\\\n$: recordingWaveform?.on(\\\\\"finish\\\\\", () => {\\\\n    dispatch(\\\\\"stop\\\\\");\\\\n    playing = false;\\\\n});\\\\nconst create_mic_waveform = () => {\\\\n    if (microphoneContainer)\\\\n        microphoneContainer.innerHTML = \\\\\"\\\\\";\\\\n    if (micWaveform !== void 0)\\\\n        micWaveform.destroy();\\\\n    if (!microphoneContainer)\\\\n        return;\\\\n    micWaveform = WaveSurfer.create({\\\\n        ...waveform_settings,\\\\n        normalize: false,\\\\n        container: microphoneContainer\\\\n    });\\\\n    record = micWaveform.registerPlugin(RecordPlugin.create());\\\\n    record?.on(\\\\\"record-end\\\\\", record_end_callback);\\\\n    record?.on(\\\\\"record-start\\\\\", record_start_callback);\\\\n    record?.on(\\\\\"record-pause\\\\\", () => {\\\\n        dispatch(\\\\\"pause_recording\\\\\");\\\\n        clearInterval(interval);\\\\n    });\\\\n    record?.on(\\\\\"record-end\\\\\", (blob) => {\\\\n        recordedAudio = URL.createObjectURL(blob);\\\\n        const microphone = microphoneContainer;\\\\n        const recording2 = recordingContainer;\\\\n        if (microphone)\\\\n            microphone.style.display = \\\\\"none\\\\\";\\\\n        if (recording2 && recordedAudio) {\\\\n            recording2.innerHTML = \\\\\"\\\\\";\\\\n            create_recording_waveform();\\\\n        }\\\\n    });\\\\n};\\\\nconst create_recording_waveform = () => {\\\\n    let recording2 = recordingContainer;\\\\n    if (!recordedAudio || !recording2)\\\\n        return;\\\\n    recordingWaveform = WaveSurfer.create({\\\\n        container: recording2,\\\\n        url: recordedAudio,\\\\n        ...waveform_settings\\\\n    });\\\\n};\\\\nconst handle_trim_audio = async (start, end) => {\\\\n    mode = \\\\\"edit\\\\\";\\\\n    const decodedData = recordingWaveform.getDecodedData();\\\\n    if (decodedData)\\\\n        await process_audio(decodedData, start, end).then(async (trimmedAudio) => {\\\\n            await dispatch_blob([trimmedAudio], \\\\\"change\\\\\");\\\\n            await dispatch_blob([trimmedAudio], \\\\\"stop_recording\\\\\");\\\\n            recordingWaveform.destroy();\\\\n            create_recording_waveform();\\\\n        });\\\\n    dispatch(\\\\\"edit\\\\\");\\\\n};\\\\nonMount(() => {\\\\n    create_mic_waveform();\\\\n    window.addEventListener(\\\\\"keydown\\\\\", (e) => {\\\\n        if (e.key === \\\\\"ArrowRight\\\\\") {\\\\n            skip_audio(recordingWaveform, 0.1);\\\\n        }\\\\n        else if (e.key === \\\\\"ArrowLeft\\\\\") {\\\\n            skip_audio(recordingWaveform, -0.1);\\\\n        }\\\\n    });\\\\n});\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"component-wrapper\\\\\">\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"microphone\\\\\"\\\\n\\\\t\\\\tbind:this={microphoneContainer}\\\\n\\\\t\\\\tdata-testid=\\\\\"microphone-waveform\\\\\"\\\\n\\\\t/>\\\\n\\\\t<div bind:this={recordingContainer} data-testid=\\\\\"recording-waveform\\\\\" />\\\\n\\\\n\\\\t{#if (timing || recordedAudio) && waveform_options.show_recording_waveform}\\\\n\\\\t\\\\t<div class=\\\\\"timestamps\\\\\">\\\\n\\\\t\\\\t\\\\t<time bind:this={timeRef} class=\\\\\"time\\\\\">0:00</time>\\\\n\\\\t\\\\t\\\\t<div>\\\\n\\\\t\\\\t\\\\t\\\\t{#if mode === \\\\\"edit\\\\\" && trimDuration > 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<time class=\\\\\"trim-duration\\\\\">{format_time(trimDuration)}</time>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{#if timing}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<time class=\\\\\"duration\\\\\">{format_time(seconds)}</time>\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<time bind:this={durationRef} class=\\\\\"duration\\\\\">0:00</time>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t{#if microphoneContainer && !recordedAudio}\\\\n\\\\t\\\\t<WaveformRecordControls\\\\n\\\\t\\\\t\\\\tbind:record\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{timing}\\\\n\\\\t\\\\t\\\\t{recording}\\\\n\\\\t\\\\t\\\\tshow_recording_waveform={waveform_options.show_recording_waveform}\\\\n\\\\t\\\\t\\\\trecord_time={format_time(seconds)}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\n\\\\t{#if recordingWaveform && recordedAudio}\\\\n\\\\t\\\\t<WaveformControls\\\\n\\\\t\\\\t\\\\tbind:waveform={recordingWaveform}\\\\n\\\\t\\\\t\\\\tcontainer={recordingContainer}\\\\n\\\\t\\\\t\\\\t{playing}\\\\n\\\\t\\\\t\\\\t{audio_duration}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\tinteractive={true}\\\\n\\\\t\\\\t\\\\t{handle_trim_audio}\\\\n\\\\t\\\\t\\\\tbind:trimDuration\\\\n\\\\t\\\\t\\\\tbind:mode\\\\n\\\\t\\\\t\\\\tshow_redo\\\\n\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.microphone {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.component-wrapper {\\\\n\\\\t\\\\tpadding: var(--size-3);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.timestamps {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tpadding: var(--size-1) 0;\\\\n\\\\t\\\\tmargin: var(--spacing-md) 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.time {\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t.duration {\\\\n\\\\t\\\\tcolor: var(--neutral-400);\\\\n\\\\t}\\\\n\\\\n\\\\t.trim-duration {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tmargin-right: var(--spacing-sm);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAmNC,yBAAY,CACX,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IACV,CAEA,gCAAmB,CAClB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,KAAK,CAAE,IACR,CAEA,yBAAY,CACX,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,CACX,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,CAAC,CACxB,MAAM,CAAE,IAAI,YAAY,CAAC,CAAC,CAC3B,CAEA,mBAAM,CACL,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,uBAAU,CACT,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,4BAAe,CACd,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,YAAY,CAAE,IAAI,YAAY,CAC/B\"}'\n};\nconst AudioRecorder = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { mode } = $$props;\n  let { i18n } = $$props;\n  let { dispatch_blob } = $$props;\n  let { waveform_settings } = $$props;\n  let { waveform_options = { show_recording_waveform: true } } = $$props;\n  let { handle_reset_value } = $$props;\n  let { editable = true } = $$props;\n  let { recording = false } = $$props;\n  let recordingWaveform;\n  let recordingContainer;\n  let microphoneContainer;\n  createEventDispatcher();\n  onMount(() => {\n    window.addEventListener(\"keydown\", (e2) => {\n      if (e2.key === \"ArrowRight\") {\n        skip_audio(recordingWaveform, 0.1);\n      } else if (e2.key === \"ArrowLeft\") {\n        skip_audio(recordingWaveform, -0.1);\n      }\n    });\n  });\n  if ($$props.mode === void 0 && $$bindings.mode && mode !== void 0)\n    $$bindings.mode(mode);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.dispatch_blob === void 0 && $$bindings.dispatch_blob && dispatch_blob !== void 0)\n    $$bindings.dispatch_blob(dispatch_blob);\n  if ($$props.waveform_settings === void 0 && $$bindings.waveform_settings && waveform_settings !== void 0)\n    $$bindings.waveform_settings(waveform_settings);\n  if ($$props.waveform_options === void 0 && $$bindings.waveform_options && waveform_options !== void 0)\n    $$bindings.waveform_options(waveform_options);\n  if ($$props.handle_reset_value === void 0 && $$bindings.handle_reset_value && handle_reset_value !== void 0)\n    $$bindings.handle_reset_value(handle_reset_value);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.recording === void 0 && $$bindings.recording && recording !== void 0)\n    $$bindings.recording(recording);\n  $$result.css.add(css$2);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    $$rendered = `<div class=\"component-wrapper svelte-9n45fh\"><div class=\"microphone svelte-9n45fh\" data-testid=\"microphone-waveform\"${add_attribute(\"this\", microphoneContainer, 0)}></div> <div data-testid=\"recording-waveform\"${add_attribute(\"this\", recordingContainer, 0)}></div> ${``} ${``} ${``} </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst css$1 = {\n  code: '.controls.svelte-1fz19cj{display:flex;align-items:center;justify-content:space-between;flex-wrap:wrap}.mic-wrap.svelte-1fz19cj{display:block;align-items:center;margin:var(--spacing-xl)}.icon.svelte-1fz19cj{width:var(--size-4);height:var(--size-4);fill:var(--primary-600);stroke:var(--primary-600)}.stop-button-paused.svelte-1fz19cj{display:none;height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--block-border-color);margin-right:5px}.stop-button-paused.svelte-1fz19cj::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.stop-button.svelte-1fz19cj::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl);animation:svelte-1fz19cj-scaling 1800ms infinite}.stop-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-20);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);align-items:center;border:1px solid var(--primary-600);margin-right:5px;display:flex}.spinner-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-24);background-color:var(--block-background-fill);border-radius:var(--radius-3xl);align-items:center;border:1px solid var(--primary-600);margin:0 var(--spacing-xl);display:flex;justify-content:space-evenly}.record-button.svelte-1fz19cj::before{content:\"\";height:var(--size-4);width:var(--size-4);border-radius:var(--radius-full);background:var(--primary-600);margin:0 var(--spacing-xl)}.record-button.svelte-1fz19cj{height:var(--size-8);width:var(--size-24);background-color:var(--block-background-fill);border-radius:var(--button-large-radius);display:flex;align-items:center;border:1px solid var(--block-border-color)}@keyframes svelte-1fz19cj-scaling{0%{background-color:var(--primary-600);scale:1}50%{background-color:var(--primary-600);scale:1.2}100%{background-color:var(--primary-600);scale:1}}',\n  map: '{\"version\":3,\"file\":\"StreamAudio.svelte\",\"sources\":[\"StreamAudio.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount } from \\\\\"svelte\\\\\";\\\\nimport { Spinner } from \\\\\"@gradio/icons\\\\\";\\\\nimport WaveSurfer from \\\\\"wavesurfer.js\\\\\";\\\\nimport RecordPlugin from \\\\\"wavesurfer.js/dist/plugins/record.js\\\\\";\\\\nimport DeviceSelect from \\\\\"../shared/DeviceSelect.svelte\\\\\";\\\\nexport let recording = false;\\\\nexport let paused_recording = false;\\\\nexport let stop;\\\\nexport let record;\\\\nexport let i18n;\\\\nexport let waveform_settings;\\\\nexport let waveform_options = {\\\\n    show_recording_waveform: true\\\\n};\\\\nexport let waiting = false;\\\\nlet micWaveform;\\\\nlet waveformRecord;\\\\nlet microphoneContainer;\\\\nlet micDevices = [];\\\\nonMount(() => {\\\\n    create_mic_waveform();\\\\n});\\\\nconst create_mic_waveform = () => {\\\\n    if (micWaveform !== void 0)\\\\n        micWaveform.destroy();\\\\n    if (!microphoneContainer)\\\\n        return;\\\\n    micWaveform = WaveSurfer.create({\\\\n        ...waveform_settings,\\\\n        height: 100,\\\\n        container: microphoneContainer\\\\n    });\\\\n    waveformRecord = micWaveform.registerPlugin(RecordPlugin.create());\\\\n};\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"mic-wrap\\\\\">\\\\n\\\\t{#if waveform_options.show_recording_waveform}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tbind:this={microphoneContainer}\\\\n\\\\t\\\\t\\\\tstyle:display={recording ? \\\\\"block\\\\\" : \\\\\"none\\\\\"}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<div class=\\\\\"controls\\\\\">\\\\n\\\\t\\\\t{#if recording && !waiting}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass={paused_recording ? \\\\\"stop-button-paused\\\\\" : \\\\\"stop-button\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\twaveformRecord?.stopMic();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstop();\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"record-icon\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"pinger\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t{paused_recording ? i18n(\\\\\"audio.pause\\\\\") : i18n(\\\\\"audio.stop\\\\\")}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{:else if recording && waiting}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"spinner-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tstop();\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Spinner />\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.waiting\\\\\")}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"record-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\twaveformRecord?.startMic();\\\\n\\\\t\\\\t\\\\t\\\\t\\\\trecord();\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"record-icon\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<span class=\\\\\"dot\\\\\" />\\\\n\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.record\\\\\")}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\n\\\\t\\\\t<DeviceSelect bind:micDevices {i18n} />\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.controls {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tflex-wrap: wrap;\\\\n\\\\t}\\\\n\\\\n\\\\t.mic-wrap {\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmargin: var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.icon {\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\tfill: var(--primary-600);\\\\n\\\\t\\\\tstroke: var(--primary-600);\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button-paused {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t\\\\tmargin-right: 5px;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button-paused::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t\\\\tanimation: scaling 1800ms infinite;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--primary-600);\\\\n\\\\t\\\\tmargin-right: 5px;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t}\\\\n\\\\n\\\\t.spinner-button {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-24);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--radius-3xl);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-evenly;\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button::before {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\tborder-radius: var(--radius-full);\\\\n\\\\t\\\\tbackground: var(--primary-600);\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t}\\\\n\\\\n\\\\t.record-button {\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\twidth: var(--size-24);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder-radius: var(--button-large-radius);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tborder: 1px solid var(--block-border-color);\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes scaling {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t50% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1.2;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\tbackground-color: var(--primary-600);\\\\n\\\\t\\\\t\\\\tscale: 1;\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA0FC,wBAAU,CACT,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,aAAa,CAC9B,SAAS,CAAE,IACZ,CAEA,wBAAU,CACT,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IAAI,YAAY,CACzB,CAEA,oBAAM,CACL,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,IAAI,CAAE,IAAI,aAAa,CAAC,CACxB,MAAM,CAAE,IAAI,aAAa,CAC1B,CAEA,kCAAoB,CACnB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,YAAY,CAAE,GACf,CAEA,kCAAmB,QAAS,CAC3B,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CAEA,2BAAY,QAAS,CACpB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,SAAS,CAAE,sBAAO,CAAC,MAAM,CAAC,QAC3B,CAEA,2BAAa,CACZ,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,YAAY,CAAE,GAAG,CACjB,OAAO,CAAE,IACV,CAEA,8BAAgB,CACf,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,YAAY,CAAC,CAChC,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,CACpC,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,YAClB,CAEA,6BAAc,QAAS,CACtB,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,aAAa,CAAE,IAAI,aAAa,CAAC,CACjC,UAAU,CAAE,IAAI,aAAa,CAAC,CAC9B,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAC3B,CAEA,6BAAe,CACd,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,aAAa,CAAE,IAAI,qBAAqB,CAAC,CACzC,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAC3C,CAEA,WAAW,sBAAQ,CAClB,EAAG,CACF,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACA,GAAI,CACH,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,GACR,CACA,IAAK,CACJ,gBAAgB,CAAE,IAAI,aAAa,CAAC,CACpC,KAAK,CAAE,CACR,CACD\"}'\n};\nconst StreamAudio = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { recording = false } = $$props;\n  let { paused_recording = false } = $$props;\n  let { stop } = $$props;\n  let { record } = $$props;\n  let { i18n } = $$props;\n  let { waveform_settings } = $$props;\n  let { waveform_options = { show_recording_waveform: true } } = $$props;\n  let { waiting = false } = $$props;\n  let microphoneContainer;\n  let micDevices = [];\n  onMount(() => {\n    create_mic_waveform();\n  });\n  const create_mic_waveform = () => {\n    return;\n  };\n  if ($$props.recording === void 0 && $$bindings.recording && recording !== void 0)\n    $$bindings.recording(recording);\n  if ($$props.paused_recording === void 0 && $$bindings.paused_recording && paused_recording !== void 0)\n    $$bindings.paused_recording(paused_recording);\n  if ($$props.stop === void 0 && $$bindings.stop && stop !== void 0)\n    $$bindings.stop(stop);\n  if ($$props.record === void 0 && $$bindings.record && record !== void 0)\n    $$bindings.record(record);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.waveform_settings === void 0 && $$bindings.waveform_settings && waveform_settings !== void 0)\n    $$bindings.waveform_settings(waveform_settings);\n  if ($$props.waveform_options === void 0 && $$bindings.waveform_options && waveform_options !== void 0)\n    $$bindings.waveform_options(waveform_options);\n  if ($$props.waiting === void 0 && $$bindings.waiting && waiting !== void 0)\n    $$bindings.waiting(waiting);\n  $$result.css.add(css$1);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    $$rendered = `<div class=\"mic-wrap svelte-1fz19cj\">${waveform_options.show_recording_waveform ? `<div${add_styles({ \"display\": recording ? \"block\" : \"none\" })}${add_attribute(\"this\", microphoneContainer, 0)}></div>` : ``} <div class=\"controls svelte-1fz19cj\">${recording && !waiting ? `<button class=\"${escape(null_to_empty(paused_recording ? \"stop-button-paused\" : \"stop-button\"), true) + \" svelte-1fz19cj\"}\"><span class=\"record-icon\" data-svelte-h=\"svelte-bla7qm\"><span class=\"pinger\"></span> <span class=\"dot\"></span></span> ${escape(paused_recording ? i18n(\"audio.pause\") : i18n(\"audio.stop\"))}</button>` : `${recording && waiting ? `<button class=\"spinner-button svelte-1fz19cj\"><div class=\"icon svelte-1fz19cj\">${validate_component(Spinner, \"Spinner\").$$render($$result, {}, {}, {})}</div> ${escape(i18n(\"audio.waiting\"))}</button>` : `<button class=\"record-button svelte-1fz19cj\"><span class=\"record-icon\" data-svelte-h=\"svelte-1dwz2xe\"><span class=\"dot\"></span></span> ${escape(i18n(\"audio.record\"))}</button>`}`} ${validate_component(DeviceSelect, \"DeviceSelect\").$$render(\n      $$result,\n      { i18n, micDevices },\n      {\n        micDevices: ($$value) => {\n          micDevices = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}</div> </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst css = {\n  code: \".audio-container.svelte-1ud6e7m{height:calc(var(--size-full) - var(--size-6));display:flex;flex-direction:column;justify-content:space-between}.audio-container.compact-audio.svelte-1ud6e7m{margin-top:calc(var(--size-8) * -1);height:auto;padding:0px;gap:var(--size-2);min-height:var(--size-5)}.compact-audio.svelte-1ud6e7m .audio-player{padding:0px}.compact-audio.svelte-1ud6e7m .controls{gap:0px;padding:0px}.compact-audio.svelte-1ud6e7m .waveform-container{height:var(--size-12) !important}.compact-audio.svelte-1ud6e7m .player-container{min-height:unset;height:auto}\",\n  map: '{\"version\":3,\"file\":\"InteractiveAudio.svelte\",\"sources\":[\"InteractiveAudio.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onDestroy, createEventDispatcher, tick } from \\\\\"svelte\\\\\";\\\\nimport { Upload, ModifyUpload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { prepare_files } from \\\\\"@gradio/client\\\\\";\\\\nimport { BlockLabel } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Music } from \\\\\"@gradio/icons\\\\\";\\\\nimport { StreamingBar } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport AudioPlayer from \\\\\"../player/AudioPlayer.svelte\\\\\";\\\\nimport AudioRecorder from \\\\\"../recorder/AudioRecorder.svelte\\\\\";\\\\nimport StreamAudio from \\\\\"../streaming/StreamAudio.svelte\\\\\";\\\\nimport { SelectSource } from \\\\\"@gradio/atoms\\\\\";\\\\nexport let value = null;\\\\nexport let label;\\\\nexport let root;\\\\nexport let loop;\\\\nexport let show_label = true;\\\\nexport let show_download_button = false;\\\\nexport let sources = [\\\\\"microphone\\\\\", \\\\\"upload\\\\\"];\\\\nexport let pending = false;\\\\nexport let streaming = false;\\\\nexport let i18n;\\\\nexport let waveform_settings;\\\\nexport let trim_region_settings = {};\\\\nexport let waveform_options = {};\\\\nexport let dragging;\\\\nexport let active_source;\\\\nexport let handle_reset_value = () => {\\\\n};\\\\nexport let editable = true;\\\\nexport let max_file_size = null;\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nexport let stream_every;\\\\nexport let uploading = false;\\\\nexport let recording = false;\\\\nexport let class_name = \\\\\"\\\\\";\\\\nlet time_limit = null;\\\\nlet stream_state = \\\\\"closed\\\\\";\\\\nexport const modify_stream = (state) => {\\\\n    if (state === \\\\\"closed\\\\\") {\\\\n        time_limit = null;\\\\n        stream_state = \\\\\"closed\\\\\";\\\\n    }\\\\n    else if (state === \\\\\"waiting\\\\\") {\\\\n        stream_state = \\\\\"waiting\\\\\";\\\\n    }\\\\n    else {\\\\n        stream_state = \\\\\"open\\\\\";\\\\n    }\\\\n};\\\\nexport const set_time_limit = (time) => {\\\\n    if (recording)\\\\n        time_limit = time;\\\\n};\\\\n$: dispatch(\\\\\"drag\\\\\", dragging);\\\\nlet recorder;\\\\nlet mode = \\\\\"\\\\\";\\\\nlet header = void 0;\\\\nlet pending_stream = [];\\\\nlet submit_pending_stream_on_pending_end = false;\\\\nlet inited = false;\\\\nconst NUM_HEADER_BYTES = 44;\\\\nlet audio_chunks = [];\\\\nlet module_promises;\\\\nfunction get_modules() {\\\\n    module_promises = [\\\\n        import(\\\\\"extendable-media-recorder\\\\\"),\\\\n        import(\\\\\"extendable-media-recorder-wav-encoder\\\\\")\\\\n    ];\\\\n}\\\\nconst is_browser = typeof window !== \\\\\"undefined\\\\\";\\\\nif (is_browser && streaming) {\\\\n    get_modules();\\\\n}\\\\nconst dispatch = createEventDispatcher();\\\\nconst dispatch_blob = async (blobs, event) => {\\\\n    let _audio_blob = new File(blobs, \\\\\"audio.wav\\\\\");\\\\n    const val = await prepare_files([_audio_blob], event === \\\\\"stream\\\\\");\\\\n    value = ((await upload(val, root, void 0, max_file_size || void 0))?.filter(Boolean))[0];\\\\n    dispatch(event, value);\\\\n};\\\\nonDestroy(() => {\\\\n    if (streaming && recorder && recorder.state !== \\\\\"inactive\\\\\") {\\\\n        recorder.stop();\\\\n    }\\\\n});\\\\nasync function prepare_audio() {\\\\n    let stream;\\\\n    try {\\\\n        stream = await navigator.mediaDevices.getUserMedia({ audio: true });\\\\n    }\\\\n    catch (err) {\\\\n        if (!navigator.mediaDevices) {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"audio.no_device_support\\\\\"));\\\\n            return;\\\\n        }\\\\n        if (err instanceof DOMException && err.name == \\\\\"NotAllowedError\\\\\") {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"audio.allow_recording_access\\\\\"));\\\\n            return;\\\\n        }\\\\n        throw err;\\\\n    }\\\\n    if (stream == null)\\\\n        return;\\\\n    if (streaming) {\\\\n        const [{ MediaRecorder: MediaRecorder2, register }, { connect }] = await Promise.all(module_promises);\\\\n        await register(await connect());\\\\n        recorder = new MediaRecorder2(stream, { mimeType: \\\\\"audio/wav\\\\\" });\\\\n        recorder.addEventListener(\\\\\"dataavailable\\\\\", handle_chunk);\\\\n    }\\\\n    else {\\\\n        recorder = new MediaRecorder(stream);\\\\n        recorder.addEventListener(\\\\\"dataavailable\\\\\", (event) => {\\\\n            audio_chunks.push(event.data);\\\\n        });\\\\n    }\\\\n    recorder.addEventListener(\\\\\"stop\\\\\", async () => {\\\\n        recording = false;\\\\n        await dispatch_blob(audio_chunks, \\\\\"change\\\\\");\\\\n        await dispatch_blob(audio_chunks, \\\\\"stop_recording\\\\\");\\\\n        audio_chunks = [];\\\\n    });\\\\n    inited = true;\\\\n}\\\\nasync function handle_chunk(event) {\\\\n    let buffer = await event.data.arrayBuffer();\\\\n    let payload = new Uint8Array(buffer);\\\\n    if (!header) {\\\\n        header = new Uint8Array(buffer.slice(0, NUM_HEADER_BYTES));\\\\n        payload = new Uint8Array(buffer.slice(NUM_HEADER_BYTES));\\\\n    }\\\\n    if (pending) {\\\\n        pending_stream.push(payload);\\\\n    }\\\\n    else {\\\\n        let blobParts = [header].concat(pending_stream, [payload]);\\\\n        if (!recording || stream_state === \\\\\"waiting\\\\\")\\\\n            return;\\\\n        dispatch_blob(blobParts, \\\\\"stream\\\\\");\\\\n        pending_stream = [];\\\\n    }\\\\n}\\\\n$: if (submit_pending_stream_on_pending_end && pending === false) {\\\\n    submit_pending_stream_on_pending_end = false;\\\\n    if (header && pending_stream) {\\\\n        let blobParts = [header].concat(pending_stream);\\\\n        pending_stream = [];\\\\n        dispatch_blob(blobParts, \\\\\"stream\\\\\");\\\\n    }\\\\n}\\\\nasync function record() {\\\\n    recording = true;\\\\n    dispatch(\\\\\"start_recording\\\\\");\\\\n    if (!inited)\\\\n        await prepare_audio();\\\\n    header = void 0;\\\\n    if (streaming && recorder.state != \\\\\"recording\\\\\") {\\\\n        recorder.start(stream_every * 1e3);\\\\n    }\\\\n}\\\\nfunction clear() {\\\\n    dispatch(\\\\\"change\\\\\", null);\\\\n    dispatch(\\\\\"clear\\\\\");\\\\n    mode = \\\\\"\\\\\";\\\\n    value = null;\\\\n}\\\\nfunction handle_load({ detail }) {\\\\n    value = detail;\\\\n    dispatch(\\\\\"change\\\\\", detail);\\\\n    dispatch(\\\\\"upload\\\\\", detail);\\\\n}\\\\nasync function stop() {\\\\n    recording = false;\\\\n    if (streaming) {\\\\n        dispatch(\\\\\"close_stream\\\\\");\\\\n        dispatch(\\\\\"stop_recording\\\\\");\\\\n        recorder.stop();\\\\n        if (pending) {\\\\n            submit_pending_stream_on_pending_end = true;\\\\n        }\\\\n        dispatch_blob(audio_chunks, \\\\\"stop_recording\\\\\");\\\\n        dispatch(\\\\\"clear\\\\\");\\\\n        mode = \\\\\"\\\\\";\\\\n    }\\\\n}\\\\n$: if (!recording && recorder)\\\\n    stop();\\\\n$: if (recording && recorder)\\\\n    record();\\\\n<\\/script>\\\\n\\\\n<BlockLabel\\\\n\\\\t{show_label}\\\\n\\\\tIcon={Music}\\\\n\\\\tfloat={active_source === \\\\\"upload\\\\\" && value === null}\\\\n\\\\tlabel={label || i18n(\\\\\"audio.audio\\\\\")}\\\\n/>\\\\n<div class=\\\\\"audio-container {class_name}\\\\\">\\\\n\\\\t<StreamingBar {time_limit} />\\\\n\\\\t{#if value === null || streaming}\\\\n\\\\t\\\\t{#if active_source === \\\\\"microphone\\\\\"}\\\\n\\\\t\\\\t\\\\t<ModifyUpload {i18n} on:clear={clear} />\\\\n\\\\t\\\\t\\\\t{#if streaming}\\\\n\\\\t\\\\t\\\\t\\\\t<StreamAudio\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{record}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{recording}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{stop}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{waveform_settings}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\twaiting={stream_state === \\\\\"waiting\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t<AudioRecorder\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tbind:mode\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{recording}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{dispatch_blob}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{waveform_settings}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:start_recording\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:pause_recording\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:stop_recording\\\\n\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{:else if active_source === \\\\\"upload\\\\\"}\\\\n\\\\t\\\\t\\\\t<!-- explicitly listed out audio mimetypes due to iOS bug not recognizing audio/* -->\\\\n\\\\t\\\\t\\\\t<Upload\\\\n\\\\t\\\\t\\\\t\\\\tfiletype=\\\\\"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:load={handle_load}\\\\n\\\\t\\\\t\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\t\\\\t\\\\tbind:uploading\\\\n\\\\t\\\\t\\\\t\\\\ton:error={({ detail }) => dispatch(\\\\\"error\\\\\", detail)}\\\\n\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t{max_file_size}\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\t\\\\taria_label={i18n(\\\\\"audio.drop_to_upload\\\\\")}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<slot />\\\\n\\\\t\\\\t\\\\t</Upload>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<ModifyUpload\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\ton:clear={clear}\\\\n\\\\t\\\\t\\\\ton:edit={() => (mode = \\\\\"edit\\\\\")}\\\\n\\\\t\\\\t\\\\tdownload={show_download_button ? value.url : null}\\\\n\\\\t\\\\t/>\\\\n\\\\n\\\\t\\\\t<AudioPlayer\\\\n\\\\t\\\\t\\\\tbind:mode\\\\n\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t{label}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{dispatch_blob}\\\\n\\\\t\\\\t\\\\t{waveform_settings}\\\\n\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t{trim_region_settings}\\\\n\\\\t\\\\t\\\\t{handle_reset_value}\\\\n\\\\t\\\\t\\\\t{editable}\\\\n\\\\t\\\\t\\\\t{loop}\\\\n\\\\t\\\\t\\\\tinteractive\\\\n\\\\t\\\\t\\\\ton:stop\\\\n\\\\t\\\\t\\\\ton:play\\\\n\\\\t\\\\t\\\\ton:pause\\\\n\\\\t\\\\t\\\\ton:edit\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<SelectSource {sources} bind:active_source handle_clear={clear} />\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.audio-container {\\\\n\\\\t\\\\theight: calc(var(--size-full) - var(--size-6));\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t}\\\\n\\\\n\\\\t.audio-container.compact-audio {\\\\n\\\\t\\\\tmargin-top: calc(var(--size-8) * -1);\\\\n\\\\t\\\\theight: auto;\\\\n\\\\t\\\\tpadding: 0px;\\\\n\\\\t\\\\tgap: var(--size-2);\\\\n\\\\t\\\\tmin-height: var(--size-5);\\\\n\\\\t}\\\\n\\\\n\\\\t.compact-audio :global(.audio-player) {\\\\n\\\\t\\\\tpadding: 0px;\\\\n\\\\t}\\\\n\\\\n\\\\t.compact-audio :global(.controls) {\\\\n\\\\t\\\\tgap: 0px;\\\\n\\\\t\\\\tpadding: 0px;\\\\n\\\\t}\\\\n\\\\n\\\\t.compact-audio :global(.waveform-container) {\\\\n\\\\t\\\\theight: var(--size-12) !important;\\\\n\\\\t}\\\\n\\\\n\\\\t.compact-audio :global(.player-container) {\\\\n\\\\t\\\\tmin-height: unset;\\\\n\\\\t\\\\theight: auto;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAkRC,+BAAiB,CAChB,MAAM,CAAE,KAAK,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAC9C,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,aAClB,CAEA,gBAAgB,6BAAe,CAC9B,UAAU,CAAE,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CACpC,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,GAAG,CACZ,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,UAAU,CAAE,IAAI,QAAQ,CACzB,CAEA,6BAAc,CAAS,aAAe,CACrC,OAAO,CAAE,GACV,CAEA,6BAAc,CAAS,SAAW,CACjC,GAAG,CAAE,GAAG,CACR,OAAO,CAAE,GACV,CAEA,6BAAc,CAAS,mBAAqB,CAC3C,MAAM,CAAE,IAAI,SAAS,CAAC,CAAC,UACxB,CAEA,6BAAc,CAAS,iBAAmB,CACzC,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,IACT\"}'\n};\nconst NUM_HEADER_BYTES = 44;\nconst InteractiveAudio = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value = null } = $$props;\n  let { label } = $$props;\n  let { root } = $$props;\n  let { loop } = $$props;\n  let { show_label = true } = $$props;\n  let { show_download_button = false } = $$props;\n  let { sources = [\"microphone\", \"upload\"] } = $$props;\n  let { pending = false } = $$props;\n  let { streaming = false } = $$props;\n  let { i18n } = $$props;\n  let { waveform_settings } = $$props;\n  let { trim_region_settings = {} } = $$props;\n  let { waveform_options = {} } = $$props;\n  let { dragging } = $$props;\n  let { active_source } = $$props;\n  let { handle_reset_value = () => {\n  } } = $$props;\n  let { editable = true } = $$props;\n  let { max_file_size = null } = $$props;\n  let { upload } = $$props;\n  let { stream_handler } = $$props;\n  let { stream_every } = $$props;\n  let { uploading = false } = $$props;\n  let { recording = false } = $$props;\n  let { class_name = \"\" } = $$props;\n  let time_limit = null;\n  let stream_state = \"closed\";\n  const modify_stream = (state) => {\n    if (state === \"closed\") {\n      time_limit = null;\n      stream_state = \"closed\";\n    } else if (state === \"waiting\") {\n      stream_state = \"waiting\";\n    } else {\n      stream_state = \"open\";\n    }\n  };\n  const set_time_limit = (time) => {\n    if (recording)\n      time_limit = time;\n  };\n  let recorder;\n  let mode = \"\";\n  let header = void 0;\n  let pending_stream = [];\n  let submit_pending_stream_on_pending_end = false;\n  let inited = false;\n  let audio_chunks = [];\n  let module_promises;\n  function get_modules() {\n    module_promises = [\n      import(\"./module3.js\"),\n      import(\"./module.js\")\n    ];\n  }\n  const is_browser = typeof window !== \"undefined\";\n  if (is_browser && streaming) {\n    get_modules();\n  }\n  const dispatch = createEventDispatcher();\n  const dispatch_blob = async (blobs, event) => {\n    let _audio_blob = new File(blobs, \"audio.wav\");\n    const val = await prepare_files([_audio_blob], event === \"stream\");\n    value = (await upload(val, root, void 0, max_file_size || void 0))?.filter(Boolean)[0];\n    dispatch(event, value);\n  };\n  onDestroy(() => {\n    if (streaming && recorder && recorder.state !== \"inactive\") {\n      recorder.stop();\n    }\n  });\n  async function prepare_audio() {\n    let stream;\n    try {\n      stream = await navigator.mediaDevices.getUserMedia({ audio: true });\n    } catch (err) {\n      if (!navigator.mediaDevices) {\n        dispatch(\"error\", i18n(\"audio.no_device_support\"));\n        return;\n      }\n      if (err instanceof DOMException && err.name == \"NotAllowedError\") {\n        dispatch(\"error\", i18n(\"audio.allow_recording_access\"));\n        return;\n      }\n      throw err;\n    }\n    if (stream == null)\n      return;\n    if (streaming) {\n      const [{ MediaRecorder: MediaRecorder2, register }, { connect }] = await Promise.all(module_promises);\n      await register(await connect());\n      recorder = new MediaRecorder2(stream, { mimeType: \"audio/wav\" });\n      recorder.addEventListener(\"dataavailable\", handle_chunk);\n    } else {\n      recorder = new MediaRecorder(stream);\n      recorder.addEventListener(\"dataavailable\", (event) => {\n        audio_chunks.push(event.data);\n      });\n    }\n    recorder.addEventListener(\"stop\", async () => {\n      recording = false;\n      await dispatch_blob(audio_chunks, \"change\");\n      await dispatch_blob(audio_chunks, \"stop_recording\");\n      audio_chunks = [];\n    });\n    inited = true;\n  }\n  async function handle_chunk(event) {\n    let buffer = await event.data.arrayBuffer();\n    let payload = new Uint8Array(buffer);\n    if (!header) {\n      header = new Uint8Array(buffer.slice(0, NUM_HEADER_BYTES));\n      payload = new Uint8Array(buffer.slice(NUM_HEADER_BYTES));\n    }\n    if (pending) {\n      pending_stream.push(payload);\n    } else {\n      let blobParts = [header].concat(pending_stream, [payload]);\n      if (!recording || stream_state === \"waiting\")\n        return;\n      dispatch_blob(blobParts, \"stream\");\n      pending_stream = [];\n    }\n  }\n  async function record() {\n    recording = true;\n    dispatch(\"start_recording\");\n    if (!inited)\n      await prepare_audio();\n    header = void 0;\n    if (streaming && recorder.state != \"recording\") {\n      recorder.start(stream_every * 1e3);\n    }\n  }\n  function clear() {\n    dispatch(\"change\", null);\n    dispatch(\"clear\");\n    mode = \"\";\n    value = null;\n  }\n  async function stop() {\n    recording = false;\n    if (streaming) {\n      dispatch(\"close_stream\");\n      dispatch(\"stop_recording\");\n      recorder.stop();\n      if (pending) {\n        submit_pending_stream_on_pending_end = true;\n      }\n      dispatch_blob(audio_chunks, \"stop_recording\");\n      dispatch(\"clear\");\n      mode = \"\";\n    }\n  }\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.loop === void 0 && $$bindings.loop && loop !== void 0)\n    $$bindings.loop(loop);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.sources === void 0 && $$bindings.sources && sources !== void 0)\n    $$bindings.sources(sources);\n  if ($$props.pending === void 0 && $$bindings.pending && pending !== void 0)\n    $$bindings.pending(pending);\n  if ($$props.streaming === void 0 && $$bindings.streaming && streaming !== void 0)\n    $$bindings.streaming(streaming);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.waveform_settings === void 0 && $$bindings.waveform_settings && waveform_settings !== void 0)\n    $$bindings.waveform_settings(waveform_settings);\n  if ($$props.trim_region_settings === void 0 && $$bindings.trim_region_settings && trim_region_settings !== void 0)\n    $$bindings.trim_region_settings(trim_region_settings);\n  if ($$props.waveform_options === void 0 && $$bindings.waveform_options && waveform_options !== void 0)\n    $$bindings.waveform_options(waveform_options);\n  if ($$props.dragging === void 0 && $$bindings.dragging && dragging !== void 0)\n    $$bindings.dragging(dragging);\n  if ($$props.active_source === void 0 && $$bindings.active_source && active_source !== void 0)\n    $$bindings.active_source(active_source);\n  if ($$props.handle_reset_value === void 0 && $$bindings.handle_reset_value && handle_reset_value !== void 0)\n    $$bindings.handle_reset_value(handle_reset_value);\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.max_file_size === void 0 && $$bindings.max_file_size && max_file_size !== void 0)\n    $$bindings.max_file_size(max_file_size);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.stream_handler === void 0 && $$bindings.stream_handler && stream_handler !== void 0)\n    $$bindings.stream_handler(stream_handler);\n  if ($$props.stream_every === void 0 && $$bindings.stream_every && stream_every !== void 0)\n    $$bindings.stream_every(stream_every);\n  if ($$props.uploading === void 0 && $$bindings.uploading && uploading !== void 0)\n    $$bindings.uploading(uploading);\n  if ($$props.recording === void 0 && $$bindings.recording && recording !== void 0)\n    $$bindings.recording(recording);\n  if ($$props.class_name === void 0 && $$bindings.class_name && class_name !== void 0)\n    $$bindings.class_name(class_name);\n  if ($$props.modify_stream === void 0 && $$bindings.modify_stream && modify_stream !== void 0)\n    $$bindings.modify_stream(modify_stream);\n  if ($$props.set_time_limit === void 0 && $$bindings.set_time_limit && set_time_limit !== void 0)\n    $$bindings.set_time_limit(set_time_limit);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      dispatch(\"drag\", dragging);\n    }\n    {\n      if (submit_pending_stream_on_pending_end && pending === false) {\n        submit_pending_stream_on_pending_end = false;\n        if (header && pending_stream) {\n          let blobParts = [header].concat(pending_stream);\n          pending_stream = [];\n          dispatch_blob(blobParts, \"stream\");\n        }\n      }\n    }\n    {\n      if (!recording && recorder)\n        stop();\n    }\n    {\n      if (recording && recorder)\n        record();\n    }\n    $$rendered = `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n      $$result,\n      {\n        show_label,\n        Icon: Music,\n        float: active_source === \"upload\" && value === null,\n        label: label || i18n(\"audio.audio\")\n      },\n      {},\n      {}\n    )} <div class=\"${\"audio-container \" + escape(class_name, true) + \" svelte-1ud6e7m\"}\">${validate_component(StreamingBar, \"StreamingBar\").$$render($$result, { time_limit }, {}, {})} ${value === null || streaming ? `${active_source === \"microphone\" ? `${validate_component(ModifyUpload, \"ModifyUpload\").$$render($$result, { i18n }, {}, {})} ${streaming ? `${validate_component(StreamAudio, \"StreamAudio\").$$render(\n      $$result,\n      {\n        record,\n        recording,\n        stop,\n        i18n,\n        waveform_settings,\n        waveform_options,\n        waiting: stream_state === \"waiting\"\n      },\n      {},\n      {}\n    )}` : `${validate_component(AudioRecorder, \"AudioRecorder\").$$render(\n      $$result,\n      {\n        i18n,\n        editable,\n        recording,\n        dispatch_blob,\n        waveform_settings,\n        waveform_options,\n        handle_reset_value,\n        mode\n      },\n      {\n        mode: ($$value) => {\n          mode = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}`}` : `${active_source === \"upload\" ? ` ${validate_component(Upload, \"Upload\").$$render(\n      $$result,\n      {\n        filetype: \"audio/aac,audio/midi,audio/mpeg,audio/ogg,audio/wav,audio/x-wav,audio/opus,audio/webm,audio/flac,audio/vnd.rn-realaudio,audio/x-ms-wma,audio/x-aiff,audio/amr,audio/*\",\n        root,\n        max_file_size,\n        upload,\n        stream_handler,\n        aria_label: i18n(\"audio.drop_to_upload\"),\n        dragging,\n        uploading\n      },\n      {\n        dragging: ($$value) => {\n          dragging = $$value;\n          $$settled = false;\n        },\n        uploading: ($$value) => {\n          uploading = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${slots.default ? slots.default({}) : ``}`;\n        }\n      }\n    )}` : ``}`}` : `${validate_component(ModifyUpload, \"ModifyUpload\").$$render(\n      $$result,\n      {\n        i18n,\n        download: show_download_button ? value.url : null\n      },\n      {},\n      {}\n    )} ${validate_component(AudioPlayer, \"AudioPlayer\").$$render(\n      $$result,\n      {\n        value,\n        label,\n        i18n,\n        dispatch_blob,\n        waveform_settings,\n        waveform_options,\n        trim_region_settings,\n        handle_reset_value,\n        editable,\n        loop,\n        interactive: true,\n        mode\n      },\n      {\n        mode: ($$value) => {\n          mode = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}`} ${validate_component(SelectSource, \"SelectSource\").$$render(\n      $$result,\n      {\n        sources,\n        handle_clear: clear,\n        active_source\n      },\n      {\n        active_source: ($$value) => {\n          active_source = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )} </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst InteractiveAudio$1 = InteractiveAudio;\nexport {\n  InteractiveAudio$1 as I\n};\n"], "names": ["AudioPlayer"], "mappings": ";;;;;AAKA,SAAS,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC3B,EAAE,OAAO,KAAK,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC,EAAE;AACpD,IAAI,SAAS,CAAC,CAAC,EAAE,EAAE;AACnB,MAAM,IAAI;AACV,QAAQ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;AACd,OAAO;AACP,KAAK;AACL,IAAI,SAAS,CAAC,CAAC,EAAE,EAAE;AACnB,MAAM,IAAI;AACV,QAAQ,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACxB,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;AACd,OAAO;AACP,KAAK;AACL,IAAI,SAAS,CAAC,CAAC,EAAE,EAAE;AACnB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC,SAAS,EAAE,EAAE;AAC3F,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACf,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACrB,KAAK;AACL,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AACtC,GAAG,CAAC,CAAC;AACL,CAAC;AACD,UAAU,IAAI,OAAO,eAAe,IAAI,eAAe,CAAC;AACxD,MAAM,CAAC,CAAC;AACR,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC7F,GAAG;AACH,EAAE,gBAAgB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AAC/B,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,mBAAmB,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE;AAC3I,MAAM,MAAM,EAAE,GAAG,MAAM;AACvB,QAAQ,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC3E,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;AAC/C,KAAK;AACL,IAAI,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAClD,GAAG;AACH,EAAE,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE;AAC9B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACzE,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE;AACf,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3C,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE;AAClB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxE,GAAG;AACH,CAAC;AACD,MAAM,CAAC,SAAS,CAAC,CAAC;AAClB,EAAE,WAAW,CAAC,EAAE,EAAE;AAClB,IAAI,KAAK,EAAE,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACxD,GAAG;AACH,EAAE,MAAM,GAAG;AACX,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;AACxC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;AACnE,GAAG;AACH,CAAC;AACD,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AAC9E,MAAM,CAAC,SAAS,CAAC,CAAC;AAClB,EAAE,WAAW,CAAC,EAAE,EAAE;AAClB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,kBAAkB,EAAE,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,kBAAkB,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC5L,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,EAAE,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,eAAe,CAAC,EAAE,EAAE;AACtB,IAAI,MAAM,EAAE,GAAG,IAAI,YAAY,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,cAAc,EAAE,CAAC;AACjG,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACnB,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC,GAAG,IAAI,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;AACtF,IAAI,IAAI,CAAC,CAAC;AACV,IAAI,MAAM,CAAC,GAAG,MAAM;AACpB,MAAM,EAAE,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,GAAG,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC;AAC3M,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,EAAE,EAAE,MAAM;AACtB,MAAM,oBAAoB,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC,UAAU,EAAE,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC;AACvF,KAAK,CAAC;AACN,GAAG;AACH,EAAE,QAAQ,CAAC,EAAE,EAAE;AACf,IAAI,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AAChD,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,IAAI;AACV,QAAQ,EAAE,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,EAAE,IAAI,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;AACrI,OAAO,CAAC,OAAO,EAAE,EAAE;AACnB,QAAQ,MAAM,IAAI,KAAK,CAAC,kCAAkC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC;AACzE,OAAO;AACP,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;AAC1C,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE,EAAE,CAAC;AACrF,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC;AACvH,GAAG;AACH,EAAE,cAAc,CAAC,EAAE,EAAE;AACrB,IAAI,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AAChD,MAAM,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,KAAK,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,aAAa,IAAI,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;AAC3P,MAAM,IAAI,CAAC,aAAa,GAAG,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;AACpD,MAAM,MAAM,EAAE,GAAG,EAAE,CAAC;AACpB,MAAM,EAAE,CAAC,eAAe,GAAG,CAAC,EAAE,KAAK;AACnC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAC7C,OAAO,EAAE,EAAE,CAAC,MAAM,GAAG,MAAM;AAC3B,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC;AACvD,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB,KAAK,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1K,OAAO,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC/C,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,WAAW,MAAM,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACrG,GAAG;AACH,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,QAAQ,MAAM,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAClG,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AAC7F,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;AACzH,GAAG;AACH,EAAE,eAAe,GAAG;AACpB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;AACxH,GAAG;AACH,EAAE,OAAO,wBAAwB,GAAG;AACpC,IAAI,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,aAAa;AAChD,MAAM,OAAO,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,YAAY,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACjH,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;AAC1D,GAAG;AACH,CAAC;AACD,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,yXAAyX;AACjY,EAAE,GAAG,EAAE,uyEAAuyE;AAC9yE,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE;AACF,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACvC,MAAM,IAAI;AACV,QAAQ,IAAI,WAAW,GAAG,EAAE,CAAC;AAC7B,QAAQ,CAAC,CAAC,wBAAwB,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK;AACvD,UAAU,UAAU,GAAG,OAAO,CAAC;AAC/B,UAAU,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACtC,YAAY,IAAI,MAAM,CAAC,QAAQ,EAAE;AACjC,cAAc,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,aAAa;AACb,WAAW,CAAC,CAAC;AACb,UAAU,UAAU,GAAG,WAAW,CAAC;AACnC,SAAS,CAAC,CAAC;AACX,OAAO,CAAC,OAAO,GAAG,EAAE;AACpB,QAAQ,IAAI,GAAG,YAAY,YAAY,IAAI,GAAG,CAAC,IAAI,IAAI,iBAAiB,EAAE;AAC1E,UAAU,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC;AAClE,SAAS;AACT,QAAQ,MAAM,GAAG,CAAC;AAClB,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,2EAA2E,EAAE,UAAU,CAAC,MAAM,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,KAAK,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC,SAAS,KAAK;AACtQ,IAAI,OAAO,CAAC,OAAO,EAAE,aAAa,CAAC,OAAO,EAAE,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC;AACzG,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAmGH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,gcAAgc;AACxc,EAAE,GAAG,EAAE,uhQAAuhQ;AAC9hQ,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACrF,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,gBAAgB,GAAG,EAAE,uBAAuB,EAAE,IAAI,EAAE,EAAE,GAAG,OAAO,CAAC;AACzE,EAAE,IAAI,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAEtC,EAAE,IAAI,kBAAkB,CAAC;AACzB,EAAE,IAAI,mBAAmB,CAAC;AAC1B,EAAE,qBAAqB,EAAE,CAAC;AAU1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,UAAU,GAAG,CAAC,oHAAoH,EAAE,aAAa,CAAC,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAAC,CAAC,6CAA6C,EAAE,aAAa,CAAC,MAAM,EAAE,kBAAkB,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACpT,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,+hEAA+hE;AACviE,EAAE,GAAG,EAAE,mtPAAmtP;AAC1tP,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACnF,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,gBAAgB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC7C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,gBAAgB,GAAG,EAAE,uBAAuB,EAAE,IAAI,EAAE,EAAE,GAAG,OAAO,CAAC;AACzE,EAAE,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,mBAAmB,CAAC;AAC1B,EAAE,IAAI,UAAU,GAAG,EAAE,CAAC;AAOtB,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,UAAU,GAAG,CAAC,qCAAqC,EAAE,gBAAgB,CAAC,uBAAuB,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,SAAS,EAAE,SAAS,GAAG,OAAO,GAAG,MAAM,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,mBAAmB,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,sCAAsC,EAAE,SAAS,IAAI,CAAC,OAAO,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,aAAa,CAAC,gBAAgB,GAAG,oBAAoB,GAAG,aAAa,CAAC,EAAE,IAAI,CAAC,GAAG,iBAAiB,CAAC,wHAAwH,EAAE,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,SAAS,IAAI,OAAO,GAAG,CAAC,+EAA+E,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,uIAAuI,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAC7jC,MAAM,QAAQ;AACd,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE;AAC1B,MAAM;AACN,QAAQ,UAAU,EAAE,CAAC,OAAO,KAAK;AACjC,UAAU,UAAU,GAAG,OAAO,CAAC;AAC/B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,aAAa,CAAC,CAAC;AACrB,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACH,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,0jBAA0jB;AAClkB,EAAE,GAAG,EAAE,woUAAwoU;AAC/oU,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAC5B,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACxF,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,oBAAoB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,EAAE,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,oBAAoB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,kBAAkB,GAAG,MAAM;AACnC,GAAG,EAAE,GAAG,OAAO,CAAC;AAChB,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,QAAQ,CAAC;AAC9B,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,IAAI,KAAK,KAAK,QAAQ,EAAE;AAC5B,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,MAAM,YAAY,GAAG,QAAQ,CAAC;AAC9B,KAAK,MAAM,IAAI,KAAK,KAAK,SAAS,EAAE;AACpC,MAAM,YAAY,GAAG,SAAS,CAAC;AAC/B,KAAK,MAAM;AACX,MAAM,YAAY,GAAG,MAAM,CAAC;AAC5B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,KAAK;AACnC,IAAI,IAAI,SAAS;AACjB,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,GAAG,CAAC;AACJ,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,CAAC;AACtB,EAAE,IAAI,cAAc,GAAG,EAAE,CAAC;AAC1B,EAAE,IAAI,oCAAoC,GAAG,KAAK,CAAC;AACnD,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;AACrB,EAAE,IAAI,YAAY,GAAG,EAAE,CAAC;AACxB,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,eAAe,GAAG;AACtB,MAAM,OAAO,uBAAc,CAAC;AAC5B,MAAM,OAAO,sBAAa,CAAC;AAC3B,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACnD,EAAE,IAAI,UAAU,IAAI,SAAS,EAAE;AAC/B,IAAI,WAAW,EAAE,CAAC;AAClB,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,MAAM,aAAa,GAAG,OAAO,KAAK,EAAE,KAAK,KAAK;AAChD,IAAI,IAAI,WAAW,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;AACnD,IAAI,MAAM,GAAG,GAAG,MAAM,aAAa,CAAC,CAAC,WAAW,CAAC,EAAE,KAAK,KAAK,QAAQ,CAAC,CAAC;AACvE,IAAI,KAAK,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,aAAa,IAAI,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3F,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,IAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,KAAK,UAAU,EAAE;AAChE,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACtB,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,eAAe,aAAa,GAAG;AACjC,IAAI,IAAI,MAAM,CAAC;AACf,IAAI,IAAI;AACR,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1E,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;AACnC,QAAQ,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC;AAC3D,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,GAAG,YAAY,YAAY,IAAI,GAAG,CAAC,IAAI,IAAI,iBAAiB,EAAE;AACxE,QAAQ,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAC;AAChE,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,MAAM,GAAG,CAAC;AAChB,KAAK;AACL,IAAI,IAAI,MAAM,IAAI,IAAI;AACtB,MAAM,OAAO;AACb,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAM,CAAC,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AAC5G,MAAM,MAAM,QAAQ,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC;AACtC,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC;AACvE,MAAM,QAAQ,CAAC,gBAAgB,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;AAC/D,KAAK,MAAM;AACX,MAAM,QAAQ,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;AAC3C,MAAM,QAAQ,CAAC,gBAAgB,CAAC,eAAe,EAAE,CAAC,KAAK,KAAK;AAC5D,QAAQ,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACtC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAAE,YAAY;AAClD,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM,MAAM,aAAa,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;AAClD,MAAM,MAAM,aAAa,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;AAC1D,MAAM,YAAY,GAAG,EAAE,CAAC;AACxB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,GAAG,IAAI,CAAC;AAClB,GAAG;AACH,EAAE,eAAe,YAAY,CAAC,KAAK,EAAE;AACrC,IAAI,IAAI,MAAM,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AAChD,IAAI,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;AACjE,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACnC,KAAK,MAAM;AACX,MAAM,IAAI,SAAS,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AACjE,MAAM,IAAI,CAAC,SAAS,IAAI,YAAY,KAAK,SAAS;AAClD,QAAQ,OAAO;AACf,MAAM,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACzC,MAAM,cAAc,GAAG,EAAE,CAAC;AAC1B,KAAK;AACL,GAAG;AACH,EAAE,eAAe,MAAM,GAAG;AAC1B,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,iBAAiB,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,MAAM;AACf,MAAM,MAAM,aAAa,EAAE,CAAC;AAC5B,IAAI,MAAM,GAAG,KAAK,CAAC,CAAC;AACpB,IAAI,IAAI,SAAS,IAAI,QAAQ,CAAC,KAAK,IAAI,WAAW,EAAE;AACpD,MAAM,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,CAAC;AACzC,KAAK;AACL,GAAG;AACH,EAAE,SAAS,KAAK,GAAG;AACnB,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC7B,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtB,IAAI,IAAI,GAAG,EAAE,CAAC;AACd,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,GAAG;AACH,EAAE,eAAe,IAAI,GAAG;AACxB,IAAI,SAAS,GAAG,KAAK,CAAC;AACtB,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,QAAQ,CAAC,cAAc,CAAC,CAAC;AAC/B,MAAM,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AACjC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACtB,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,oCAAoC,GAAG,IAAI,CAAC;AACpD,OAAO;AACP,MAAM,aAAa,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACpD,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC;AACxB,MAAM,IAAI,GAAG,EAAE,CAAC;AAChB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,oCAAoC,IAAI,OAAO,KAAK,KAAK,EAAE;AACrE,QAAQ,oCAAoC,GAAG,KAAK,CAAC;AACrD,QAAQ,IAAI,MAAM,IAAI,cAAc,EAAE;AACtC,UAAU,IAAI,SAAS,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAC1D,UAAU,cAAc,GAAG,EAAE,CAAC;AAC9B,UAAU,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC7C,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,CAAC,SAAS,IAAI,QAAQ;AAChC,QAAQ,IAAI,EAAE,CAAC;AACf,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,SAAS,IAAI,QAAQ;AAC/B,QAAQ,MAAM,EAAE,CAAC;AACjB,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACzE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,UAAU;AAClB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,KAAK,EAAE,aAAa,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI;AAC3D,QAAQ,KAAK,EAAE,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC;AAC3C,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,aAAa,EAAE,kBAAkB,GAAG,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,iBAAiB,CAAC,EAAE,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,SAAS,GAAG,CAAC,EAAE,aAAa,KAAK,YAAY,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,kBAAkB,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ;AAC9Z,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,MAAM;AACd,QAAQ,SAAS;AACjB,QAAQ,IAAI;AACZ,QAAQ,IAAI;AACZ,QAAQ,iBAAiB;AACzB,QAAQ,gBAAgB;AACxB,QAAQ,OAAO,EAAE,YAAY,KAAK,SAAS;AAC3C,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,QAAQ;AACxE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,IAAI;AACZ,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,QAAQ,aAAa;AACrB,QAAQ,iBAAiB;AACzB,QAAQ,gBAAgB;AACxB,QAAQ,kBAAkB;AAC1B,QAAQ,IAAI;AACZ,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,IAAI,GAAG,OAAO,CAAC;AACzB,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,aAAa,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC7F,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,QAAQ,EAAE,uKAAuK;AACzL,QAAQ,IAAI;AACZ,QAAQ,aAAa;AACrB,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC;AAChD,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,OAAO,KAAK;AAChC,UAAU,SAAS,GAAG,OAAO,CAAC;AAC9B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAC/E,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,IAAI;AACZ,QAAQ,QAAQ,EAAE,oBAAoB,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI;AACzD,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,EAAE,kBAAkB,CAACA,aAAW,EAAE,aAAa,CAAC,CAAC,QAAQ;AAChE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,KAAK;AACb,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,QAAQ,aAAa;AACrB,QAAQ,iBAAiB;AACzB,QAAQ,gBAAgB;AACxB,QAAQ,oBAAoB;AAC5B,QAAQ,kBAAkB;AAC1B,QAAQ,QAAQ;AAChB,QAAQ,IAAI;AACZ,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,IAAI;AACZ,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,IAAI,GAAG,OAAO,CAAC;AACzB,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AACpE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,YAAY,EAAE,KAAK;AAC3B,QAAQ,aAAa;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,CAAC,OAAO,KAAK;AACpC,UAAU,aAAa,GAAG,OAAO,CAAC;AAClC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,OAAO,CAAC,CAAC;AACf,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,kBAAkB,GAAG;;;;"}