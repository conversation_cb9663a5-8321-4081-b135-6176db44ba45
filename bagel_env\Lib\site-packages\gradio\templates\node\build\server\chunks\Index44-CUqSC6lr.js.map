{"version": 3, "file": "Index44-CUqSC6lr.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index44.js"], "sourcesContent": ["import { create_ssr_component, add_attribute, validate_component, escape, each } from \"svelte/internal\";\nimport { createEventDispatcher, beforeUpdate, onMount, afterUpdate } from \"svelte\";\nimport { r as BlockTitle, G as Clear, M as Music, V as Video, K as File, N as Paperclip, O as Microphone, z as Send, A as Square, n as Block, S as Static } from \"./client.js\";\nimport { U as Upload } from \"./ModifyUpload.js\";\nimport { I as Image } from \"./Image.js\";\nimport \"./ImagePreview.js\";\nimport { I as InteractiveAudio } from \"./InteractiveAudio.js\";\nimport { default as default2 } from \"./Example20.js\";\nconst css = {\n  code: '.full-container.svelte-5gfv2q.svelte-5gfv2q{width:100%;position:relative;padding:var(--block-padding);border:1px solid transparent}.full-container.dragging.svelte-5gfv2q.svelte-5gfv2q{border:1px solid var(--color-accent);border-radius:calc(var(--radius-sm) - 1px)}.full-container.dragging.svelte-5gfv2q.svelte-5gfv2q::after{content:\"\";position:absolute;top:0;left:0;right:0;bottom:0;pointer-events:none}.input-container.svelte-5gfv2q.svelte-5gfv2q{display:flex;position:relative;align-items:flex-end}textarea.svelte-5gfv2q.svelte-5gfv2q{flex-grow:1;outline:none !important;background:var(--block-background-fill);padding:var(--input-padding);color:var(--body-text-color);font-weight:var(--input-text-weight);font-size:var(--input-text-size);line-height:var(--line-sm);border:none;margin-top:0px;margin-bottom:0px;resize:none;position:relative;z-index:1;text-align:left}textarea[dir=\"rtl\"].svelte-5gfv2q.svelte-5gfv2q{text-align:right}textarea[dir=\"rtl\"].svelte-5gfv2q~.submit-button.svelte-5gfv2q{order:-1;margin-left:0;margin-right:var(--spacing-sm)}textarea[dir=\"rtl\"].svelte-5gfv2q~.submit-button.svelte-5gfv2q svg{transform:scaleX(-1)}textarea.no-label.svelte-5gfv2q.svelte-5gfv2q{padding-top:5px;padding-bottom:5px}textarea.svelte-5gfv2q.svelte-5gfv2q:disabled{-webkit-opacity:1;opacity:1}textarea.svelte-5gfv2q.svelte-5gfv2q::placeholder{color:var(--input-placeholder-color)}.microphone-button.svelte-5gfv2q.svelte-5gfv2q,.upload-button.svelte-5gfv2q.svelte-5gfv2q,.submit-button.svelte-5gfv2q.svelte-5gfv2q,.stop-button.svelte-5gfv2q.svelte-5gfv2q{border:none;text-align:center;text-decoration:none;font-size:14px;cursor:pointer;border-radius:15px;min-width:30px;height:30px;flex-shrink:0;display:flex;justify-content:center;align-items:center;z-index:var(--layer-1);margin-left:var(--spacing-sm)}.padded-button.svelte-5gfv2q.svelte-5gfv2q{padding:0 10px}.microphone-button.svelte-5gfv2q.svelte-5gfv2q,.stop-button.svelte-5gfv2q.svelte-5gfv2q,.upload-button.svelte-5gfv2q.svelte-5gfv2q,.submit-button.svelte-5gfv2q.svelte-5gfv2q{background:var(--button-secondary-background-fill)}.microphone-button.svelte-5gfv2q.svelte-5gfv2q:hover:not(:disabled),.stop-button.svelte-5gfv2q.svelte-5gfv2q:hover:not(:disabled),.upload-button.svelte-5gfv2q.svelte-5gfv2q:hover:not(:disabled),.submit-button.svelte-5gfv2q.svelte-5gfv2q:hover:not(:disabled){background:var(--button-secondary-background-fill-hover)}.microphone-button.svelte-5gfv2q.svelte-5gfv2q:disabled,.stop-button.svelte-5gfv2q.svelte-5gfv2q:disabled,.upload-button.svelte-5gfv2q.svelte-5gfv2q:disabled,.submit-button.svelte-5gfv2q.svelte-5gfv2q:disabled{background:var(--button-secondary-background-fill);cursor:not-allowed}.microphone-button.svelte-5gfv2q.svelte-5gfv2q:active,.stop-button.svelte-5gfv2q.svelte-5gfv2q:active,.upload-button.svelte-5gfv2q.svelte-5gfv2q:active,.submit-button.svelte-5gfv2q.svelte-5gfv2q:active{box-shadow:var(--button-shadow-active)}.submit-button.svelte-5gfv2q svg{height:22px;width:22px}.microphone-button.svelte-5gfv2q svg,.upload-button.svelte-5gfv2q svg{height:17px;width:17px}.stop-button.svelte-5gfv2q svg{height:16px;width:16px}.loader.svelte-5gfv2q.svelte-5gfv2q{display:flex;justify-content:center;align-items:center;--ring-color:transparent;position:relative;border:5px solid #f3f3f3;border-top:5px solid var(--color-accent);border-radius:50%;width:25px;height:25px;animation:svelte-5gfv2q-spin 2s linear infinite}@keyframes svelte-5gfv2q-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}.thumbnails.svelte-5gfv2q img{width:var(--size-full);height:var(--size-full);object-fit:cover;border-radius:var(--radius-lg)}.thumbnails.svelte-5gfv2q.svelte-5gfv2q{display:flex;align-items:center;gap:var(--spacing-lg);overflow-x:scroll;padding-top:var(--spacing-sm);margin-bottom:6px}.thumbnail-item.svelte-5gfv2q.svelte-5gfv2q{display:flex;justify-content:center;align-items:center;--ring-color:transparent;position:relative;box-shadow:0 0 0 2px var(--ring-color),\\n\t\t\tvar(--shadow-drop);border:1px solid var(--border-color-primary);border-radius:var(--radius-lg);background:var(--background-fill-secondary);aspect-ratio:var(--ratio-square);width:var(--size-full);height:var(--size-full);cursor:default}.thumbnail-small.svelte-5gfv2q.svelte-5gfv2q{flex:none;transform:scale(0.9);transition:0.075s;width:var(--size-12);height:var(--size-12)}.thumbnail-item.svelte-5gfv2q svg{width:30px;height:30px}.delete-button.svelte-5gfv2q.svelte-5gfv2q{display:flex;justify-content:center;align-items:center;position:absolute;right:-7px;top:-7px;color:var(--button-secondary-text-color);background:var(--button-secondary-background-fill);border:none;text-align:center;text-decoration:none;font-size:10px;cursor:pointer;border-radius:50%;width:20px;height:20px}.disabled.svelte-5gfv2q.svelte-5gfv2q{display:none}.delete-button.svelte-5gfv2q svg{width:12px;height:12px}.delete-button.svelte-5gfv2q.svelte-5gfv2q:hover{filter:brightness(1.2);border:0.8px solid var(--color-grey-500)}',\n  map: '{\"version\":3,\"file\":\"MultimodalTextbox.svelte\",\"sources\":[\"MultimodalTextbox.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onMount, beforeUpdate, afterUpdate, createEventDispatcher, tick } from \\\\\"svelte\\\\\";\\\\nimport { text_area_resize, resize } from \\\\\"../shared/utils\\\\\";\\\\nimport { BlockTitle } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Upload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { Image } from \\\\\"@gradio/image/shared\\\\\";\\\\nimport { Clear, File, Music, Paperclip, Video, Send, Square, Microphone } from \\\\\"@gradio/icons\\\\\";\\\\nimport InteractiveAudio from \\\\\"../../audio/interactive/InteractiveAudio.svelte\\\\\";\\\\nexport let value = {\\\\n    text: \\\\\"\\\\\",\\\\n    files: []\\\\n};\\\\nexport let value_is_output = false;\\\\nexport let lines = 1;\\\\nexport let i18n;\\\\nexport let placeholder = \\\\\"Type here...\\\\\";\\\\nexport let disabled = false;\\\\nexport let label;\\\\nexport let info = void 0;\\\\nexport let show_label = true;\\\\nexport let max_lines;\\\\nexport let submit_btn = null;\\\\nexport let stop_btn = null;\\\\nexport let rtl = false;\\\\nexport let autofocus = false;\\\\nexport let text_align = void 0;\\\\nexport let autoscroll = true;\\\\nexport let root;\\\\nexport let file_types = null;\\\\nexport let max_file_size = null;\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nexport let file_count = \\\\\"multiple\\\\\";\\\\nexport let max_plain_text_length = 1e3;\\\\nexport let waveform_settings;\\\\nexport let waveform_options = {\\\\n    show_recording_waveform: true\\\\n};\\\\nexport let sources = [\\\\\"upload\\\\\"];\\\\nexport let active_source = null;\\\\nlet upload_component;\\\\nlet el;\\\\nlet can_scroll;\\\\nlet previous_scroll_top = 0;\\\\nlet user_has_scrolled_up = false;\\\\nexport let dragging = false;\\\\nlet uploading = false;\\\\nlet oldValue = value.text;\\\\nlet recording = false;\\\\n$: dispatch(\\\\\"drag\\\\\", dragging);\\\\nlet mic_audio = null;\\\\nlet full_container;\\\\n$: if (oldValue !== value.text) {\\\\n    dispatch(\\\\\"change\\\\\", value);\\\\n    oldValue = value.text;\\\\n}\\\\n$: if (value === null)\\\\n    value = { text: \\\\\"\\\\\", files: [] };\\\\n$: value, el && lines !== max_lines && resize(el, lines, max_lines);\\\\nconst dispatch = createEventDispatcher();\\\\nbeforeUpdate(() => {\\\\n    can_scroll = el && el.offsetHeight + el.scrollTop > el.scrollHeight - 100;\\\\n});\\\\nconst scroll = () => {\\\\n    if (can_scroll && autoscroll && !user_has_scrolled_up) {\\\\n        el.scrollTo(0, el.scrollHeight);\\\\n    }\\\\n};\\\\nasync function handle_change() {\\\\n    dispatch(\\\\\"change\\\\\", value);\\\\n    if (!value_is_output) {\\\\n        dispatch(\\\\\"input\\\\\");\\\\n    }\\\\n}\\\\nonMount(() => {\\\\n    if (autofocus && el !== null) {\\\\n        el.focus();\\\\n    }\\\\n});\\\\nafterUpdate(() => {\\\\n    if (can_scroll && autoscroll) {\\\\n        scroll();\\\\n    }\\\\n    value_is_output = false;\\\\n});\\\\nfunction handle_select(event) {\\\\n    const target = event.target;\\\\n    const text = target.value;\\\\n    const index = [\\\\n        target.selectionStart,\\\\n        target.selectionEnd\\\\n    ];\\\\n    dispatch(\\\\\"select\\\\\", { value: text.substring(...index), index });\\\\n}\\\\nasync function handle_keypress(e) {\\\\n    await tick();\\\\n    if (e.key === \\\\\"Enter\\\\\" && e.shiftKey && lines > 1) {\\\\n        e.preventDefault();\\\\n        dispatch(\\\\\"submit\\\\\");\\\\n    }\\\\n    else if (e.key === \\\\\"Enter\\\\\" && !e.shiftKey && lines === 1 && max_lines >= 1) {\\\\n        e.preventDefault();\\\\n        dispatch(\\\\\"submit\\\\\");\\\\n        active_source = null;\\\\n        if (mic_audio) {\\\\n            value.files.push(mic_audio);\\\\n            value = value;\\\\n            mic_audio = null;\\\\n        }\\\\n    }\\\\n}\\\\nfunction handle_scroll(event) {\\\\n    const target = event.target;\\\\n    const current_scroll_top = target.scrollTop;\\\\n    if (current_scroll_top < previous_scroll_top) {\\\\n        user_has_scrolled_up = true;\\\\n    }\\\\n    previous_scroll_top = current_scroll_top;\\\\n    const max_scroll_top = target.scrollHeight - target.clientHeight;\\\\n    const user_has_scrolled_to_bottom = current_scroll_top >= max_scroll_top;\\\\n    if (user_has_scrolled_to_bottom) {\\\\n        user_has_scrolled_up = false;\\\\n    }\\\\n}\\\\nasync function handle_upload({ detail }) {\\\\n    handle_change();\\\\n    if (Array.isArray(detail)) {\\\\n        for (let file of detail) {\\\\n            value.files.push(file);\\\\n        }\\\\n        value = value;\\\\n    }\\\\n    else {\\\\n        value.files.push(detail);\\\\n        value = value;\\\\n    }\\\\n    await tick();\\\\n    dispatch(\\\\\"change\\\\\", value);\\\\n    dispatch(\\\\\"upload\\\\\", detail);\\\\n}\\\\nfunction remove_thumbnail(event, index) {\\\\n    handle_change();\\\\n    event.stopPropagation();\\\\n    value.files.splice(index, 1);\\\\n    value = value;\\\\n}\\\\nfunction handle_upload_click() {\\\\n    upload_component.open_upload();\\\\n}\\\\nfunction handle_stop() {\\\\n    dispatch(\\\\\"stop\\\\\");\\\\n}\\\\nfunction handle_submit() {\\\\n    dispatch(\\\\\"submit\\\\\");\\\\n    active_source = null;\\\\n    if (mic_audio) {\\\\n        value.files.push(mic_audio);\\\\n        value = value;\\\\n        mic_audio = null;\\\\n    }\\\\n}\\\\nasync function handle_paste(event) {\\\\n    if (!event.clipboardData)\\\\n        return;\\\\n    const items = event.clipboardData.items;\\\\n    const text = event.clipboardData.getData(\\\\\"text\\\\\");\\\\n    if (text && text.length > max_plain_text_length) {\\\\n        event.preventDefault();\\\\n        const file = new window.File([text], \\\\\"pasted_text.txt\\\\\", {\\\\n            type: \\\\\"text/plain\\\\\",\\\\n            lastModified: Date.now()\\\\n        });\\\\n        if (upload_component) {\\\\n            upload_component.load_files([file]);\\\\n        }\\\\n        return;\\\\n    }\\\\n    for (let index in items) {\\\\n        const item = items[index];\\\\n        if (item.kind === \\\\\"file\\\\\" && item.type.includes(\\\\\"image\\\\\")) {\\\\n            const blob = item.getAsFile();\\\\n            if (blob)\\\\n                upload_component.load_files([blob]);\\\\n        }\\\\n    }\\\\n}\\\\nfunction handle_dragenter(event) {\\\\n    event.preventDefault();\\\\n    dragging = true;\\\\n}\\\\nfunction handle_dragleave(event) {\\\\n    event.preventDefault();\\\\n    const rect = full_container.getBoundingClientRect();\\\\n    const { clientX, clientY } = event;\\\\n    if (clientX <= rect.left || clientX >= rect.right || clientY <= rect.top || clientY >= rect.bottom) {\\\\n        dragging = false;\\\\n    }\\\\n}\\\\nfunction handle_drop(event) {\\\\n    event.preventDefault();\\\\n    dragging = false;\\\\n    if (event.dataTransfer && event.dataTransfer.files) {\\\\n        const files = Array.from(event.dataTransfer.files);\\\\n        if (file_types) {\\\\n            const valid_files = files.filter((file) => {\\\\n                return file_types.some((type) => {\\\\n                    if (type.startsWith(\\\\\".\\\\\")) {\\\\n                        return file.name.toLowerCase().endsWith(type.toLowerCase());\\\\n                    }\\\\n                    return file.type.match(new RegExp(type.replace(\\\\\"*\\\\\", \\\\\".*\\\\\")));\\\\n                });\\\\n            });\\\\n            const invalid_files = files.length - valid_files.length;\\\\n            if (invalid_files > 0) {\\\\n                dispatch(\\\\\"error\\\\\", `${invalid_files} file(s) were rejected. Accepted formats: ${file_types.join(\\\\\", \\\\\")}`);\\\\n            }\\\\n            if (valid_files.length > 0) {\\\\n                upload_component.load_files(valid_files);\\\\n            }\\\\n        }\\\\n        else {\\\\n            upload_component.load_files(files);\\\\n        }\\\\n    }\\\\n}\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"full-container\\\\\"\\\\n\\\\tclass:dragging\\\\n\\\\tbind:this={full_container}\\\\n\\\\ton:dragenter={handle_dragenter}\\\\n\\\\ton:dragleave={handle_dragleave}\\\\n\\\\ton:dragover|preventDefault\\\\n\\\\ton:drop={handle_drop}\\\\n\\\\trole=\\\\\"group\\\\\"\\\\n\\\\taria-label=\\\\\"Multimedia input field\\\\\"\\\\n>\\\\n\\\\t<BlockTitle {root} {show_label} {info} {rtl}>{label}</BlockTitle>\\\\n\\\\t{#if value.files.length > 0 || uploading}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tclass=\\\\\"thumbnails scroll-hide\\\\\"\\\\n\\\\t\\\\t\\\\taria-label=\\\\\"Uploaded files\\\\\"\\\\n\\\\t\\\\t\\\\tdata-testid=\\\\\"container_el\\\\\"\\\\n\\\\t\\\\t\\\\tstyle=\\\\\"display: {value.files.length > 0 || uploading ? \\'flex\\' : \\'none\\'};\\\\\"\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#each value.files as file, index}\\\\n\\\\t\\\\t\\\\t\\\\t<span role=\\\\\"listitem\\\\\" aria-label=\\\\\"File thumbnail\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<button class=\\\\\"thumbnail-item thumbnail-small\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:disabled\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"delete-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={(event) => remove_thumbnail(event, index)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t><Clear /></button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if file.mime_type && file.mime_type.includes(\\\\\"image\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Image\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tsrc={file.url}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ttitle={null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\talt=\\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tloading=\\\\\"lazy\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass={\\\\\"thumbnail-image\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if file.mime_type && file.mime_type.includes(\\\\\"audio\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Music />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else if file.mime_type && file.mime_type.includes(\\\\\"video\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Video />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<File />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t{#if uploading}\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"loader\\\\\" role=\\\\\"status\\\\\" aria-label=\\\\\"Uploading\\\\\"></div>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\t{#if sources && sources.includes(\\\\\"microphone\\\\\") && active_source === \\\\\"microphone\\\\\"}\\\\n\\\\t\\\\t<InteractiveAudio\\\\n\\\\t\\\\t\\\\ton:change={({ detail }) => {\\\\n\\\\t\\\\t\\\\t\\\\tif (detail !== null) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tmic_audio = detail;\\\\n\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\ton:clear={() => {\\\\n\\\\t\\\\t\\\\t\\\\tactive_source = null;\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\ton:start_recording={() => dispatch(\\\\\"start_recording\\\\\")}\\\\n\\\\t\\\\t\\\\ton:pause_recording={() => dispatch(\\\\\"pause_recording\\\\\")}\\\\n\\\\t\\\\t\\\\ton:stop_recording={() => dispatch(\\\\\"stop_recording\\\\\")}\\\\n\\\\t\\\\t\\\\tsources={[\\\\\"microphone\\\\\"]}\\\\n\\\\t\\\\t\\\\tclass_name=\\\\\"compact-audio\\\\\"\\\\n\\\\t\\\\t\\\\t{recording}\\\\n\\\\t\\\\t\\\\t{waveform_settings}\\\\n\\\\t\\\\t\\\\t{waveform_options}\\\\n\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t{active_source}\\\\n\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\tstream_every={1}\\\\n\\\\t\\\\t\\\\teditable={true}\\\\n\\\\t\\\\t\\\\t{label}\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\tloop={false}\\\\n\\\\t\\\\t\\\\tshow_label={false}\\\\n\\\\t\\\\t\\\\tshow_download_button={false}\\\\n\\\\t\\\\t\\\\tdragging={false}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n\\\\t<div class=\\\\\"input-container\\\\\">\\\\n\\\\t\\\\t{#if sources && sources.includes(\\\\\"upload\\\\\") && !(file_count === \\\\\"single\\\\\" && value.files.length > 0)}\\\\n\\\\t\\\\t\\\\t<Upload\\\\n\\\\t\\\\t\\\\t\\\\tbind:this={upload_component}\\\\n\\\\t\\\\t\\\\t\\\\ton:load={handle_upload}\\\\n\\\\t\\\\t\\\\t\\\\t{file_count}\\\\n\\\\t\\\\t\\\\t\\\\tfiletype={file_types}\\\\n\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t{max_file_size}\\\\n\\\\t\\\\t\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\t\\\\t\\\\tbind:uploading\\\\n\\\\t\\\\t\\\\t\\\\tshow_progress={false}\\\\n\\\\t\\\\t\\\\t\\\\tdisable_click={true}\\\\n\\\\t\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t\\\\thidden={true}\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"upload-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"upload-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t{disabled}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={disabled ? undefined : handle_upload_click}\\\\n\\\\t\\\\t\\\\t\\\\t><Paperclip /></button\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{#if sources && sources.includes(\\\\\"microphone\\\\\")}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tdata-testid=\\\\\"microphone-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"microphone-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:recording\\\\n\\\\t\\\\t\\\\t\\\\t{disabled}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={disabled\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t? undefined\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t: () => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tactive_source =\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tactive_source !== \\\\\"microphone\\\\\" ? \\\\\"microphone\\\\\" : null;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<Microphone />\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t<!-- svelte-ignore a11y-autofocus -->\\\\n\\\\t\\\\t<textarea\\\\n\\\\t\\\\t\\\\tdata-testid=\\\\\"textbox\\\\\"\\\\n\\\\t\\\\t\\\\tuse:text_area_resize={{\\\\n\\\\t\\\\t\\\\t\\\\ttext: value.text,\\\\n\\\\t\\\\t\\\\t\\\\tlines: lines,\\\\n\\\\t\\\\t\\\\t\\\\tmax_lines: max_lines\\\\n\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\tclass=\\\\\"scroll-hide\\\\\"\\\\n\\\\t\\\\t\\\\tclass:no-label={!show_label}\\\\n\\\\t\\\\t\\\\tdir={rtl ? \\\\\"rtl\\\\\" : \\\\\"ltr\\\\\"}\\\\n\\\\t\\\\t\\\\tbind:value={value.text}\\\\n\\\\t\\\\t\\\\tbind:this={el}\\\\n\\\\t\\\\t\\\\t{placeholder}\\\\n\\\\t\\\\t\\\\trows={lines}\\\\n\\\\t\\\\t\\\\t{disabled}\\\\n\\\\t\\\\t\\\\t{autofocus}\\\\n\\\\t\\\\t\\\\ton:keypress={handle_keypress}\\\\n\\\\t\\\\t\\\\ton:blur\\\\n\\\\t\\\\t\\\\ton:select={handle_select}\\\\n\\\\t\\\\t\\\\ton:focus\\\\n\\\\t\\\\t\\\\ton:scroll={handle_scroll}\\\\n\\\\t\\\\t\\\\ton:paste={handle_paste}\\\\n\\\\t\\\\t\\\\tstyle={text_align ? \\\\\"text-align: \\\\\" + text_align : \\\\\"\\\\\"}\\\\n\\\\t\\\\t/>\\\\n\\\\t\\\\t{#if submit_btn}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"submit-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:padded-button={submit_btn !== true}\\\\n\\\\t\\\\t\\\\t\\\\t{disabled}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={disabled ? undefined : handle_submit}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if submit_btn === true}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Send />\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{submit_btn}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t\\\\t{#if stop_btn}\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"stop-button\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:padded-button={stop_btn !== true}\\\\n\\\\t\\\\t\\\\t\\\\ton:click={handle_stop}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if stop_btn === true}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<Square fill={\\\\\"none\\\\\"} stroke_width={2.5} />\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{stop_btn}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.full-container {\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tpadding: var(--block-padding);\\\\n\\\\t\\\\tborder: 1px solid transparent;\\\\n\\\\t}\\\\n\\\\n\\\\t.full-container.dragging {\\\\n\\\\t\\\\tborder: 1px solid var(--color-accent);\\\\n\\\\t\\\\tborder-radius: calc(var(--radius-sm) - 1px);\\\\n\\\\t}\\\\n\\\\n\\\\t.full-container.dragging::after {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tright: 0;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tpointer-events: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.input-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\talign-items: flex-end;\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea {\\\\n\\\\t\\\\tflex-grow: 1;\\\\n\\\\t\\\\toutline: none !important;\\\\n\\\\t\\\\tbackground: var(--block-background-fill);\\\\n\\\\t\\\\tpadding: var(--input-padding);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-weight: var(--input-text-weight);\\\\n\\\\t\\\\tfont-size: var(--input-text-size);\\\\n\\\\t\\\\tline-height: var(--line-sm);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tmargin-top: 0px;\\\\n\\\\t\\\\tmargin-bottom: 0px;\\\\n\\\\t\\\\tresize: none;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tz-index: 1;\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t}\\\\n\\\\ttextarea[dir=\\\\\"rtl\\\\\"] {\\\\n\\\\t\\\\ttext-align: right;\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea[dir=\\\\\"rtl\\\\\"] ~ .submit-button {\\\\n\\\\t\\\\torder: -1;\\\\n\\\\t\\\\tmargin-left: 0;\\\\n\\\\t\\\\tmargin-right: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea[dir=\\\\\"rtl\\\\\"] ~ .submit-button :global(svg) {\\\\n\\\\t\\\\ttransform: scaleX(-1);\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea.no-label {\\\\n\\\\t\\\\tpadding-top: 5px;\\\\n\\\\t\\\\tpadding-bottom: 5px;\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea:disabled {\\\\n\\\\t\\\\t-webkit-opacity: 1;\\\\n\\\\t\\\\topacity: 1;\\\\n\\\\t}\\\\n\\\\n\\\\ttextarea::placeholder {\\\\n\\\\t\\\\tcolor: var(--input-placeholder-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.microphone-button,\\\\n\\\\t.upload-button,\\\\n\\\\t.submit-button,\\\\n\\\\t.stop-button {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tfont-size: 14px;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tborder-radius: 15px;\\\\n\\\\t\\\\tmin-width: 30px;\\\\n\\\\t\\\\theight: 30px;\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tz-index: var(--layer-1);\\\\n\\\\t\\\\tmargin-left: var(--spacing-sm);\\\\n\\\\t}\\\\n\\\\t.padded-button {\\\\n\\\\t\\\\tpadding: 0 10px;\\\\n\\\\t}\\\\n\\\\n\\\\t.microphone-button,\\\\n\\\\t.stop-button,\\\\n\\\\t.upload-button,\\\\n\\\\t.submit-button {\\\\n\\\\t\\\\tbackground: var(--button-secondary-background-fill);\\\\n\\\\t}\\\\n\\\\n\\\\t.microphone-button:hover:not(:disabled),\\\\n\\\\t.stop-button:hover:not(:disabled),\\\\n\\\\t.upload-button:hover:not(:disabled),\\\\n\\\\t.submit-button:hover:not(:disabled) {\\\\n\\\\t\\\\tbackground: var(--button-secondary-background-fill-hover);\\\\n\\\\t}\\\\n\\\\n\\\\t.microphone-button:disabled,\\\\n\\\\t.stop-button:disabled,\\\\n\\\\t.upload-button:disabled,\\\\n\\\\t.submit-button:disabled {\\\\n\\\\t\\\\tbackground: var(--button-secondary-background-fill);\\\\n\\\\t\\\\tcursor: not-allowed;\\\\n\\\\t}\\\\n\\\\t.microphone-button:active,\\\\n\\\\t.stop-button:active,\\\\n\\\\t.upload-button:active,\\\\n\\\\t.submit-button:active {\\\\n\\\\t\\\\tbox-shadow: var(--button-shadow-active);\\\\n\\\\t}\\\\n\\\\n\\\\t.submit-button :global(svg) {\\\\n\\\\t\\\\theight: 22px;\\\\n\\\\t\\\\twidth: 22px;\\\\n\\\\t}\\\\n\\\\t.microphone-button :global(svg),\\\\n\\\\t.upload-button :global(svg) {\\\\n\\\\t\\\\theight: 17px;\\\\n\\\\t\\\\twidth: 17px;\\\\n\\\\t}\\\\n\\\\n\\\\t.stop-button :global(svg) {\\\\n\\\\t\\\\theight: 16px;\\\\n\\\\t\\\\twidth: 16px;\\\\n\\\\t}\\\\n\\\\n\\\\t.loader {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\t--ring-color: transparent;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tborder: 5px solid #f3f3f3;\\\\n\\\\t\\\\tborder-top: 5px solid var(--color-accent);\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\twidth: 25px;\\\\n\\\\t\\\\theight: 25px;\\\\n\\\\t\\\\tanimation: spin 2s linear infinite;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes spin {\\\\n\\\\t\\\\t0% {\\\\n\\\\t\\\\t\\\\ttransform: rotate(0deg);\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t100% {\\\\n\\\\t\\\\t\\\\ttransform: rotate(360deg);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnails :global(img) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnails {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tgap: var(--spacing-lg);\\\\n\\\\t\\\\toverflow-x: scroll;\\\\n\\\\t\\\\tpadding-top: var(--spacing-sm);\\\\n\\\\t\\\\tmargin-bottom: 6px;\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\t--ring-color: transparent;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tbox-shadow:\\\\n\\\\t\\\\t\\\\t0 0 0 2px var(--ring-color),\\\\n\\\\t\\\\t\\\\tvar(--shadow-drop);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-lg);\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\taspect-ratio: var(--ratio-square);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tcursor: default;\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-small {\\\\n\\\\t\\\\tflex: none;\\\\n\\\\t\\\\ttransform: scale(0.9);\\\\n\\\\t\\\\ttransition: 0.075s;\\\\n\\\\t\\\\twidth: var(--size-12);\\\\n\\\\t\\\\theight: var(--size-12);\\\\n\\\\t}\\\\n\\\\n\\\\t.thumbnail-item :global(svg) {\\\\n\\\\t\\\\twidth: 30px;\\\\n\\\\t\\\\theight: 30px;\\\\n\\\\t}\\\\n\\\\n\\\\t.delete-button {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tright: -7px;\\\\n\\\\t\\\\ttop: -7px;\\\\n\\\\t\\\\tcolor: var(--button-secondary-text-color);\\\\n\\\\t\\\\tbackground: var(--button-secondary-background-fill);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\ttext-align: center;\\\\n\\\\t\\\\ttext-decoration: none;\\\\n\\\\t\\\\tfont-size: 10px;\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\twidth: 20px;\\\\n\\\\t\\\\theight: 20px;\\\\n\\\\t}\\\\n\\\\n\\\\t.disabled {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.delete-button :global(svg) {\\\\n\\\\t\\\\twidth: 12px;\\\\n\\\\t\\\\theight: 12px;\\\\n\\\\t}\\\\n\\\\n\\\\t.delete-button:hover {\\\\n\\\\t\\\\tfilter: brightness(1.2);\\\\n\\\\t\\\\tborder: 0.8px solid var(--color-grey-500);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAuZC,2CAAgB,CACf,KAAK,CAAE,IAAI,CACX,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,eAAe,CAAC,CAC7B,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,WACnB,CAEA,eAAe,qCAAU,CACxB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,CACrC,aAAa,CAAE,KAAK,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAC3C,CAEA,eAAe,qCAAS,OAAQ,CAC/B,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,CAAC,CACT,cAAc,CAAE,IACjB,CAEA,4CAAiB,CAChB,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,WAAW,CAAE,QACd,CAEA,oCAAS,CACR,SAAS,CAAE,CAAC,CACZ,OAAO,CAAE,IAAI,CAAC,UAAU,CACxB,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,OAAO,CAAE,IAAI,eAAe,CAAC,CAC7B,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,WAAW,CAAE,IAAI,mBAAmB,CAAC,CACrC,SAAS,CAAE,IAAI,iBAAiB,CAAC,CACjC,WAAW,CAAE,IAAI,SAAS,CAAC,CAC3B,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,GAAG,CACf,aAAa,CAAE,GAAG,CAClB,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,IACb,CACA,QAAQ,CAAC,GAAG,CAAC,KAAK,6BAAE,CACnB,UAAU,CAAE,KACb,CAEA,QAAQ,CAAC,GAAG,CAAC,KAAK,eAAC,CAAG,4BAAe,CACpC,KAAK,CAAE,EAAE,CACT,WAAW,CAAE,CAAC,CACd,YAAY,CAAE,IAAI,YAAY,CAC/B,CAEA,QAAQ,CAAC,GAAG,CAAC,KAAK,eAAC,CAAG,4BAAc,CAAS,GAAK,CACjD,SAAS,CAAE,OAAO,EAAE,CACrB,CAEA,QAAQ,qCAAU,CACjB,WAAW,CAAE,GAAG,CAChB,cAAc,CAAE,GACjB,CAEA,oCAAQ,SAAU,CACjB,eAAe,CAAE,CAAC,CAClB,OAAO,CAAE,CACV,CAEA,oCAAQ,aAAc,CACrB,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,8CAAkB,CAClB,0CAAc,CACd,0CAAc,CACd,wCAAa,CACZ,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,MAAM,CAClB,eAAe,CAAE,IAAI,CACrB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,IAAI,CACnB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,WAAW,CAAE,IAAI,YAAY,CAC9B,CACA,0CAAe,CACd,OAAO,CAAE,CAAC,CAAC,IACZ,CAEA,8CAAkB,CAClB,wCAAY,CACZ,0CAAc,CACd,0CAAe,CACd,UAAU,CAAE,IAAI,kCAAkC,CACnD,CAEA,8CAAkB,MAAM,KAAK,SAAS,CAAC,CACvC,wCAAY,MAAM,KAAK,SAAS,CAAC,CACjC,0CAAc,MAAM,KAAK,SAAS,CAAC,CACnC,0CAAc,MAAM,KAAK,SAAS,CAAE,CACnC,UAAU,CAAE,IAAI,wCAAwC,CACzD,CAEA,8CAAkB,SAAS,CAC3B,wCAAY,SAAS,CACrB,0CAAc,SAAS,CACvB,0CAAc,SAAU,CACvB,UAAU,CAAE,IAAI,kCAAkC,CAAC,CACnD,MAAM,CAAE,WACT,CACA,8CAAkB,OAAO,CACzB,wCAAY,OAAO,CACnB,0CAAc,OAAO,CACrB,0CAAc,OAAQ,CACrB,UAAU,CAAE,IAAI,sBAAsB,CACvC,CAEA,4BAAc,CAAS,GAAK,CAC3B,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CACA,gCAAkB,CAAS,GAAI,CAC/B,4BAAc,CAAS,GAAK,CAC3B,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CAEA,0BAAY,CAAS,GAAK,CACzB,MAAM,CAAE,IAAI,CACZ,KAAK,CAAE,IACR,CAEA,mCAAQ,CACP,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,OAAO,CACzB,UAAU,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,CACzC,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,kBAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAC3B,CAEA,WAAW,kBAAK,CACf,EAAG,CACF,SAAS,CAAE,OAAO,IAAI,CACvB,CACA,IAAK,CACJ,SAAS,CAAE,OAAO,MAAM,CACzB,CACD,CAEA,yBAAW,CAAS,GAAK,CACxB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,IAAI,WAAW,CAC/B,CAEA,uCAAY,CACX,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,GAAG,CAAE,IAAI,YAAY,CAAC,CACtB,UAAU,CAAE,MAAM,CAClB,WAAW,CAAE,IAAI,YAAY,CAAC,CAC9B,aAAa,CAAE,GAChB,CAEA,2CAAgB,CACf,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,YAAY,CAAE,WAAW,CACzB,QAAQ,CAAE,QAAQ,CAClB,UAAU,CACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,YAAY,CAAC,CAAC;AAC/B,GAAG,IAAI,aAAa,CAAC,CACnB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,YAAY,CAAE,IAAI,cAAc,CAAC,CACjC,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,MAAM,CAAE,OACT,CAEA,4CAAiB,CAChB,IAAI,CAAE,IAAI,CACV,SAAS,CAAE,MAAM,GAAG,CAAC,CACrB,UAAU,CAAE,MAAM,CAClB,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,SAAS,CACtB,CAEA,6BAAe,CAAS,GAAK,CAC5B,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,0CAAe,CACd,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,CACX,GAAG,CAAE,IAAI,CACT,KAAK,CAAE,IAAI,6BAA6B,CAAC,CACzC,UAAU,CAAE,IAAI,kCAAkC,CAAC,CACnD,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,MAAM,CAClB,eAAe,CAAE,IAAI,CACrB,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,OAAO,CACf,aAAa,CAAE,GAAG,CAClB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,qCAAU,CACT,OAAO,CAAE,IACV,CAEA,4BAAc,CAAS,GAAK,CAC3B,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,0CAAc,MAAO,CACpB,MAAM,CAAE,WAAW,GAAG,CAAC,CACvB,MAAM,CAAE,KAAK,CAAC,KAAK,CAAC,IAAI,gBAAgB,CACzC\"}'\n};\nlet recording = false;\nconst MultimodalTextbox = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value = { text: \"\", files: [] } } = $$props;\n  let { value_is_output = false } = $$props;\n  let { lines = 1 } = $$props;\n  let { i18n } = $$props;\n  let { placeholder = \"Type here...\" } = $$props;\n  let { disabled = false } = $$props;\n  let { label } = $$props;\n  let { info = void 0 } = $$props;\n  let { show_label = true } = $$props;\n  let { max_lines } = $$props;\n  let { submit_btn = null } = $$props;\n  let { stop_btn = null } = $$props;\n  let { rtl = false } = $$props;\n  let { autofocus = false } = $$props;\n  let { text_align = void 0 } = $$props;\n  let { autoscroll = true } = $$props;\n  let { root } = $$props;\n  let { file_types = null } = $$props;\n  let { max_file_size = null } = $$props;\n  let { upload } = $$props;\n  let { stream_handler } = $$props;\n  let { file_count = \"multiple\" } = $$props;\n  let { max_plain_text_length = 1e3 } = $$props;\n  let { waveform_settings } = $$props;\n  let { waveform_options = { show_recording_waveform: true } } = $$props;\n  let { sources = [\"upload\"] } = $$props;\n  let { active_source = null } = $$props;\n  let upload_component;\n  let el;\n  let can_scroll;\n  let user_has_scrolled_up = false;\n  let { dragging = false } = $$props;\n  let uploading = false;\n  let oldValue = value.text;\n  let full_container;\n  const dispatch = createEventDispatcher();\n  beforeUpdate(() => {\n    can_scroll = el;\n  });\n  const scroll = () => {\n    if (can_scroll && autoscroll && !user_has_scrolled_up) {\n      el.scrollTo(0, el.scrollHeight);\n    }\n  };\n  onMount(() => {\n    if (autofocus && el !== null) {\n      el.focus();\n    }\n  });\n  afterUpdate(() => {\n    if (can_scroll && autoscroll) {\n      scroll();\n    }\n    value_is_output = false;\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.value_is_output === void 0 && $$bindings.value_is_output && value_is_output !== void 0)\n    $$bindings.value_is_output(value_is_output);\n  if ($$props.lines === void 0 && $$bindings.lines && lines !== void 0)\n    $$bindings.lines(lines);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.placeholder === void 0 && $$bindings.placeholder && placeholder !== void 0)\n    $$bindings.placeholder(placeholder);\n  if ($$props.disabled === void 0 && $$bindings.disabled && disabled !== void 0)\n    $$bindings.disabled(disabled);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.info === void 0 && $$bindings.info && info !== void 0)\n    $$bindings.info(info);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.max_lines === void 0 && $$bindings.max_lines && max_lines !== void 0)\n    $$bindings.max_lines(max_lines);\n  if ($$props.submit_btn === void 0 && $$bindings.submit_btn && submit_btn !== void 0)\n    $$bindings.submit_btn(submit_btn);\n  if ($$props.stop_btn === void 0 && $$bindings.stop_btn && stop_btn !== void 0)\n    $$bindings.stop_btn(stop_btn);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.autofocus === void 0 && $$bindings.autofocus && autofocus !== void 0)\n    $$bindings.autofocus(autofocus);\n  if ($$props.text_align === void 0 && $$bindings.text_align && text_align !== void 0)\n    $$bindings.text_align(text_align);\n  if ($$props.autoscroll === void 0 && $$bindings.autoscroll && autoscroll !== void 0)\n    $$bindings.autoscroll(autoscroll);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.file_types === void 0 && $$bindings.file_types && file_types !== void 0)\n    $$bindings.file_types(file_types);\n  if ($$props.max_file_size === void 0 && $$bindings.max_file_size && max_file_size !== void 0)\n    $$bindings.max_file_size(max_file_size);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.stream_handler === void 0 && $$bindings.stream_handler && stream_handler !== void 0)\n    $$bindings.stream_handler(stream_handler);\n  if ($$props.file_count === void 0 && $$bindings.file_count && file_count !== void 0)\n    $$bindings.file_count(file_count);\n  if ($$props.max_plain_text_length === void 0 && $$bindings.max_plain_text_length && max_plain_text_length !== void 0)\n    $$bindings.max_plain_text_length(max_plain_text_length);\n  if ($$props.waveform_settings === void 0 && $$bindings.waveform_settings && waveform_settings !== void 0)\n    $$bindings.waveform_settings(waveform_settings);\n  if ($$props.waveform_options === void 0 && $$bindings.waveform_options && waveform_options !== void 0)\n    $$bindings.waveform_options(waveform_options);\n  if ($$props.sources === void 0 && $$bindings.sources && sources !== void 0)\n    $$bindings.sources(sources);\n  if ($$props.active_source === void 0 && $$bindings.active_source && active_source !== void 0)\n    $$bindings.active_source(active_source);\n  if ($$props.dragging === void 0 && $$bindings.dragging && dragging !== void 0)\n    $$bindings.dragging(dragging);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      dispatch(\"drag\", dragging);\n    }\n    {\n      if (value === null)\n        value = { text: \"\", files: [] };\n    }\n    {\n      if (oldValue !== value.text) {\n        dispatch(\"change\", value);\n        oldValue = value.text;\n      }\n    }\n    $$rendered = `<div class=\"${[\"full-container svelte-5gfv2q\", dragging ? \"dragging\" : \"\"].join(\" \").trim()}\" role=\"group\" aria-label=\"Multimedia input field\"${add_attribute(\"this\", full_container, 0)}>${validate_component(BlockTitle, \"BlockTitle\").$$render($$result, { root, show_label, info, rtl }, {}, {\n      default: () => {\n        return `${escape(label)}`;\n      }\n    })} ${value.files.length > 0 || uploading ? `<div class=\"thumbnails scroll-hide svelte-5gfv2q\" aria-label=\"Uploaded files\" data-testid=\"container_el\" style=\"${\"display: \" + escape(value.files.length > 0 || uploading ? \"flex\" : \"none\", true) + \";\"}\">${each(value.files, (file, index) => {\n      return `<span role=\"listitem\" aria-label=\"File thumbnail\"><button class=\"thumbnail-item thumbnail-small svelte-5gfv2q\"><button class=\"${[\"delete-button svelte-5gfv2q\", disabled ? \"disabled\" : \"\"].join(\" \").trim()}\">${validate_component(Clear, \"Clear\").$$render($$result, {}, {}, {})}</button> ${file.mime_type && file.mime_type.includes(\"image\") ? `${validate_component(Image, \"Image\").$$render(\n        $$result,\n        {\n          src: file.url,\n          title: null,\n          alt: \"\",\n          loading: \"lazy\",\n          class: \"thumbnail-image\"\n        },\n        {},\n        {}\n      )}` : `${file.mime_type && file.mime_type.includes(\"audio\") ? `${validate_component(Music, \"Music\").$$render($$result, {}, {}, {})}` : `${file.mime_type && file.mime_type.includes(\"video\") ? `${validate_component(Video, \"Video\").$$render($$result, {}, {}, {})}` : `${validate_component(File, \"File\").$$render($$result, {}, {}, {})}`}`}`}</button> </span>`;\n    })} ${uploading ? `<div class=\"loader svelte-5gfv2q\" role=\"status\" aria-label=\"Uploading\"></div>` : ``}</div>` : ``} ${sources && sources.includes(\"microphone\") && active_source === \"microphone\" ? `${validate_component(InteractiveAudio, \"InteractiveAudio\").$$render(\n      $$result,\n      {\n        sources: [\"microphone\"],\n        class_name: \"compact-audio\",\n        recording,\n        waveform_settings,\n        waveform_options,\n        i18n,\n        active_source,\n        upload,\n        stream_handler,\n        stream_every: 1,\n        editable: true,\n        label,\n        root,\n        loop: false,\n        show_label: false,\n        show_download_button: false,\n        dragging: false\n      },\n      {},\n      {}\n    )}` : ``} <div class=\"input-container svelte-5gfv2q\">${sources && sources.includes(\"upload\") && !(file_count === \"single\" && value.files.length > 0) ? `${validate_component(Upload, \"Upload\").$$render(\n      $$result,\n      {\n        file_count,\n        filetype: file_types,\n        root,\n        max_file_size,\n        show_progress: false,\n        disable_click: true,\n        hidden: true,\n        upload,\n        stream_handler,\n        this: upload_component,\n        dragging,\n        uploading\n      },\n      {\n        this: ($$value) => {\n          upload_component = $$value;\n          $$settled = false;\n        },\n        dragging: ($$value) => {\n          dragging = $$value;\n          $$settled = false;\n        },\n        uploading: ($$value) => {\n          uploading = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )} <button data-testid=\"upload-button\" class=\"upload-button svelte-5gfv2q\" ${disabled ? \"disabled\" : \"\"}>${validate_component(Paperclip, \"Paperclip\").$$render($$result, {}, {}, {})}</button>` : ``} ${sources && sources.includes(\"microphone\") ? `<button data-testid=\"microphone-button\" class=\"${[\"microphone-button svelte-5gfv2q\", \"\"].join(\" \").trim()}\" ${disabled ? \"disabled\" : \"\"}>${validate_component(Microphone, \"Microphone\").$$render($$result, {}, {}, {})}</button>` : ``}  <textarea data-testid=\"textbox\" class=\"${[\"scroll-hide svelte-5gfv2q\", !show_label ? \"no-label\" : \"\"].join(\" \").trim()}\"${add_attribute(\"dir\", rtl ? \"rtl\" : \"ltr\", 0)}${add_attribute(\"placeholder\", placeholder, 0)}${add_attribute(\"rows\", lines, 0)} ${disabled ? \"disabled\" : \"\"} ${autofocus ? \"autofocus\" : \"\"}${add_attribute(\"style\", text_align ? \"text-align: \" + text_align : \"\", 0)}${add_attribute(\"this\", el, 0)}>${escape(value.text || \"\")}</textarea> ${submit_btn ? `<button class=\"${[\"submit-button svelte-5gfv2q\", submit_btn !== true ? \"padded-button\" : \"\"].join(\" \").trim()}\" ${disabled ? \"disabled\" : \"\"}>${submit_btn === true ? `${validate_component(Send, \"Send\").$$render($$result, {}, {}, {})}` : `${escape(submit_btn)}`}</button>` : ``} ${stop_btn ? `<button class=\"${[\"stop-button svelte-5gfv2q\", stop_btn !== true ? \"padded-button\" : \"\"].join(\" \").trim()}\">${stop_btn === true ? `${validate_component(Square, \"Square\").$$render($$result, { fill: \"none\", stroke_width: 2.5 }, {}, {})}` : `${escape(stop_btn)}`}</button>` : ``}</div> </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst MultimodalTextbox$1 = MultimodalTextbox;\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { gradio } = $$props;\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value = { text: \"\", files: [] } } = $$props;\n  let { file_types = null } = $$props;\n  let { lines } = $$props;\n  let { placeholder = \"\" } = $$props;\n  let { label = \"MultimodalTextbox\" } = $$props;\n  let { info = void 0 } = $$props;\n  let { show_label } = $$props;\n  let { max_lines } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { submit_btn = null } = $$props;\n  let { stop_btn = null } = $$props;\n  let { loading_status = void 0 } = $$props;\n  let { value_is_output = false } = $$props;\n  let { rtl = false } = $$props;\n  let { text_align = void 0 } = $$props;\n  let { autofocus = false } = $$props;\n  let { autoscroll = true } = $$props;\n  let { interactive } = $$props;\n  let { root } = $$props;\n  let { file_count } = $$props;\n  let { max_plain_text_length } = $$props;\n  let { sources = [\"upload\"] } = $$props;\n  let { waveform_options = {} } = $$props;\n  let dragging;\n  let active_source = null;\n  let waveform_settings;\n  let color_accent = \"darkorange\";\n  onMount(() => {\n    color_accent = getComputedStyle(document?.documentElement).getPropertyValue(\"--color-accent\");\n    set_trim_region_colour();\n    waveform_settings.waveColor = waveform_options.waveform_color || \"#9ca3af\";\n    waveform_settings.progressColor = waveform_options.waveform_progress_color || color_accent;\n    waveform_settings.mediaControls = waveform_options.show_controls;\n    waveform_settings.sampleRate = waveform_options.sample_rate || 44100;\n  });\n  const trim_region_settings = {\n    color: waveform_options.trim_region_color,\n    drag: true,\n    resize: true\n  };\n  function set_trim_region_colour() {\n    document.documentElement.style.setProperty(\"--trim-region-color\", trim_region_settings.color || color_accent);\n  }\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.file_types === void 0 && $$bindings.file_types && file_types !== void 0)\n    $$bindings.file_types(file_types);\n  if ($$props.lines === void 0 && $$bindings.lines && lines !== void 0)\n    $$bindings.lines(lines);\n  if ($$props.placeholder === void 0 && $$bindings.placeholder && placeholder !== void 0)\n    $$bindings.placeholder(placeholder);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.info === void 0 && $$bindings.info && info !== void 0)\n    $$bindings.info(info);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.max_lines === void 0 && $$bindings.max_lines && max_lines !== void 0)\n    $$bindings.max_lines(max_lines);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.submit_btn === void 0 && $$bindings.submit_btn && submit_btn !== void 0)\n    $$bindings.submit_btn(submit_btn);\n  if ($$props.stop_btn === void 0 && $$bindings.stop_btn && stop_btn !== void 0)\n    $$bindings.stop_btn(stop_btn);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.value_is_output === void 0 && $$bindings.value_is_output && value_is_output !== void 0)\n    $$bindings.value_is_output(value_is_output);\n  if ($$props.rtl === void 0 && $$bindings.rtl && rtl !== void 0)\n    $$bindings.rtl(rtl);\n  if ($$props.text_align === void 0 && $$bindings.text_align && text_align !== void 0)\n    $$bindings.text_align(text_align);\n  if ($$props.autofocus === void 0 && $$bindings.autofocus && autofocus !== void 0)\n    $$bindings.autofocus(autofocus);\n  if ($$props.autoscroll === void 0 && $$bindings.autoscroll && autoscroll !== void 0)\n    $$bindings.autoscroll(autoscroll);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.file_count === void 0 && $$bindings.file_count && file_count !== void 0)\n    $$bindings.file_count(file_count);\n  if ($$props.max_plain_text_length === void 0 && $$bindings.max_plain_text_length && max_plain_text_length !== void 0)\n    $$bindings.max_plain_text_length(max_plain_text_length);\n  if ($$props.sources === void 0 && $$bindings.sources && sources !== void 0)\n    $$bindings.sources(sources);\n  if ($$props.waveform_options === void 0 && $$bindings.waveform_options && waveform_options !== void 0)\n    $$bindings.waveform_options(waveform_options);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    waveform_settings = {\n      height: 50,\n      barWidth: 2,\n      barGap: 3,\n      cursorWidth: 2,\n      cursorColor: \"#ddd5e9\",\n      autoplay: false,\n      barRadius: 10,\n      dragToSeek: true,\n      normalize: true,\n      minPxPerSec: 20\n    };\n    $$rendered = `   ${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        elem_id,\n        elem_classes: [...elem_classes, \"multimodal-textbox\"],\n        scale,\n        min_width,\n        allow_overflow: false,\n        padding: false,\n        border_mode: dragging ? \"focus\" : \"base\"\n      },\n      {},\n      {\n        default: () => {\n          return `${loading_status ? `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})}` : ``} ${validate_component(MultimodalTextbox$1, \"MultimodalTextbox\").$$render(\n            $$result,\n            {\n              file_types,\n              root,\n              label,\n              info,\n              show_label,\n              lines,\n              rtl,\n              text_align,\n              waveform_settings,\n              i18n: gradio.i18n,\n              max_lines: !max_lines ? lines + 1 : max_lines,\n              placeholder,\n              submit_btn,\n              stop_btn,\n              autofocus,\n              autoscroll,\n              file_count,\n              sources,\n              max_file_size: gradio.max_file_size,\n              disabled: !interactive,\n              upload: (...args) => gradio.client.upload(...args),\n              stream_handler: (...args) => gradio.client.stream(...args),\n              max_plain_text_length,\n              value,\n              value_is_output,\n              dragging,\n              active_source\n            },\n            {\n              value: ($$value) => {\n                value = $$value;\n                $$settled = false;\n              },\n              value_is_output: ($$value) => {\n                value_is_output = $$value;\n                $$settled = false;\n              },\n              dragging: ($$value) => {\n                dragging = $$value;\n                $$settled = false;\n              },\n              active_source: ($$value) => {\n                active_source = $$value;\n                $$settled = false;\n              }\n            },\n            {}\n          )}`;\n        }\n      }\n    )}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  default2 as BaseExample,\n  MultimodalTextbox$1 as BaseMultimodalTextbox,\n  Index as default\n};\n"], "names": ["File", "InteractiveAudio"], "mappings": ";;;;;;;;;;;;;;;AAQA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,62JAA62J;AACr3J,EAAE,GAAG,EAAE,qpuBAAqpuB;AAC5puB,CAAC,CAAC;AACF,IAAI,SAAS,GAAG,KAAK,CAAC;AACtB,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACzF,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;AACpD,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,WAAW,GAAG,cAAc,EAAE,GAAG,OAAO,CAAC;AACjD,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,UAAU,GAAG,UAAU,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,qBAAqB,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC;AAChD,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,gBAAgB,GAAG,EAAE,uBAAuB,EAAE,IAAI,EAAE,EAAE,GAAG,OAAO,CAAC;AACzE,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,gBAAgB,CAAC;AACvB,EAAE,IAAI,EAAE,CAAC;AAGT,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC;AACxB,EAAE,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;AAC5B,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAoB3C,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,qBAAqB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,qBAAqB,IAAI,qBAAqB,KAAK,KAAK,CAAC;AACtH,IAAI,UAAU,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC;AAC5D,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,KAAK,KAAK,IAAI;AACxB,QAAQ,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;AACxC,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,QAAQ,KAAK,KAAK,CAAC,IAAI,EAAE;AACnC,QAAQ,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAClC,QAAQ,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;AAC9B,OAAO;AACP,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,YAAY,EAAE,CAAC,8BAA8B,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,kDAAkD,EAAE,aAAa,CAAC,MAAM,EAAE,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE;AACnT,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,GAAG,CAAC,gHAAgH,EAAE,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,GAAG,MAAM,GAAG,MAAM,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK;AAClS,MAAM,OAAO,CAAC,8HAA8H,EAAE,CAAC,6BAA6B,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAChZ,QAAQ,QAAQ;AAChB,QAAQ;AACR,UAAU,GAAG,EAAE,IAAI,CAAC,GAAG;AACvB,UAAU,KAAK,EAAE,IAAI;AACrB,UAAU,GAAG,EAAE,EAAE;AACjB,UAAU,OAAO,EAAE,MAAM;AACzB,UAAU,KAAK,EAAE,iBAAiB;AAClC,SAAS;AACT,QAAQ,EAAE;AACV,QAAQ,EAAE;AACV,OAAO,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAACA,MAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC;AAC1W,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,6EAA6E,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,aAAa,KAAK,YAAY,GAAG,CAAC,EAAE,kBAAkB,CAACC,kBAAgB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AAC7Q,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO,EAAE,CAAC,YAAY,CAAC;AAC/B,QAAQ,UAAU,EAAE,eAAe;AACnC,QAAQ,SAAS;AACjB,QAAQ,iBAAiB;AACzB,QAAQ,gBAAgB;AACxB,QAAQ,IAAI;AACZ,QAAQ,aAAa;AACrB,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,KAAK;AACb,QAAQ,IAAI;AACZ,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,oBAAoB,EAAE,KAAK;AACnC,QAAQ,QAAQ,EAAE,KAAK;AACvB,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,4CAA4C,EAAE,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,KAAK,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC3M,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,UAAU;AAClB,QAAQ,QAAQ,EAAE,UAAU;AAC5B,QAAQ,IAAI;AACZ,QAAQ,aAAa;AACrB,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,aAAa,EAAE,IAAI;AAC3B,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,IAAI,EAAE,gBAAgB;AAC9B,QAAQ,QAAQ;AAChB,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,gBAAgB,GAAG,OAAO,CAAC;AACrC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,OAAO,KAAK;AAChC,UAAU,SAAS,GAAG,OAAO,CAAC;AAC9B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,yEAAyE,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,EAAE,kBAAkB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,+CAA+C,EAAE,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,yCAAyC,EAAE,CAAC,2BAA2B,EAAE,CAAC,UAAU,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,aAAa,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,EAAE,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC,EAAE,aAAa,CAAC,OAAO,EAAE,UAAU,GAAG,cAAc,GAAG,UAAU,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,YAAY,EAAE,UAAU,GAAG,CAAC,eAAe,EAAE,CAAC,6BAA6B,EAAE,UAAU,KAAK,IAAI,GAAG,eAAe,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,EAAE,UAAU,KAAK,IAAI,GAAG,CAAC,EAAE,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,eAAe,EAAE,CAAC,2BAA2B,EAAE,QAAQ,KAAK,IAAI,GAAG,eAAe,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;AAClgD,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,mBAAmB,GAAG,kBAAkB;AACzC,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;AACpD,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,GAAG,mBAAmB,EAAE,GAAG,OAAO,CAAC;AAChD,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,cAAc,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,qBAAqB,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,gBAAgB,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,iBAAiB,CAAC;AAUxB,GAA+B;AAC/B,IAAI,KAAK,EAAE,gBAAgB,CAAC,iBAAiB;AAC7C,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,IAAI;AAChB,KAAI;AAIJ,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,CAAC;AAChE,IAAI,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,qBAAqB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,qBAAqB,IAAI,qBAAqB,KAAK,KAAK,CAAC;AACtH,IAAI,UAAU,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,CAAC;AAC5D,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,iBAAiB,GAAG;AACxB,MAAM,MAAM,EAAE,EAAE;AAChB,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,WAAW,EAAE,CAAC;AACpB,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,QAAQ,EAAE,KAAK;AACrB,MAAM,SAAS,EAAE,EAAE;AACnB,MAAM,UAAU,EAAE,IAAI;AACtB,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,WAAW,EAAE,EAAE;AACrB,KAAK,CAAC;AACN,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAClE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,YAAY,EAAE,CAAC,GAAG,YAAY,EAAE,oBAAoB,CAAC;AAC7D,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,WAAW,EAAE,QAAQ,GAAG,OAAO,GAAG,MAAM;AAChD,OAAO;AACP,MAAM,EAAE;AACR,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,cAAc,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC,QAAQ;AAC3R,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,UAAU;AACxB,cAAc,IAAI;AAClB,cAAc,KAAK;AACnB,cAAc,IAAI;AAClB,cAAc,UAAU;AACxB,cAAc,KAAK;AACnB,cAAc,GAAG;AACjB,cAAc,UAAU;AACxB,cAAc,iBAAiB;AAC/B,cAAc,IAAI,EAAE,MAAM,CAAC,IAAI;AAC/B,cAAc,SAAS,EAAE,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS;AAC3D,cAAc,WAAW;AACzB,cAAc,UAAU;AACxB,cAAc,QAAQ;AACtB,cAAc,SAAS;AACvB,cAAc,UAAU;AACxB,cAAc,UAAU;AACxB,cAAc,OAAO;AACrB,cAAc,aAAa,EAAE,MAAM,CAAC,aAAa;AACjD,cAAc,QAAQ,EAAE,CAAC,WAAW;AACpC,cAAc,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAChE,cAAc,cAAc,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACxE,cAAc,qBAAqB;AACnC,cAAc,KAAK;AACnB,cAAc,eAAe;AAC7B,cAAc,QAAQ;AACtB,cAAc,aAAa;AAC3B,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,CAAC,OAAO,KAAK;AAClC,gBAAgB,KAAK,GAAG,OAAO,CAAC;AAChC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,eAAe,EAAE,CAAC,OAAO,KAAK;AAC5C,gBAAgB,eAAe,GAAG,OAAO,CAAC;AAC1C,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,QAAQ,EAAE,CAAC,OAAO,KAAK;AACrC,gBAAgB,QAAQ,GAAG,OAAO,CAAC;AACnC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,aAAa,EAAE,CAAC,OAAO,KAAK;AAC1C,gBAAgB,aAAa,GAAG,OAAO,CAAC;AACxC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,aAAa;AACb,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}