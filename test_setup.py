#!/usr/bin/env python3
"""
Test script to verify BAGEL setup
"""

import sys
import os

def test_imports():
    """Test if all required packages can be imported"""
    print("🧪 Testing package imports...")
    
    try:
        import torch
        print(f"✅ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import transformers
        print(f"✅ Transformers {transformers.__version__}")
    except ImportError as e:
        print(f"❌ Transformers import failed: {e}")
        return False
    
    try:
        import gradio
        print(f"✅ Gradio {gradio.__version__}")
    except ImportError as e:
        print(f"❌ Gradio import failed: {e}")
        return False
    
    try:
        import numpy
        print(f"✅ NumPy {numpy.__version__}")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        import PIL
        print(f"✅ Pillow {PIL.__version__}")
    except ImportError as e:
        print(f"❌ Pillow import failed: {e}")
        return False
    
    return True

def test_model_files():
    """Check if model files exist"""
    print("\n📁 Checking model files...")
    
    model_path = "models/BAGEL-7B-MoT"
    if not os.path.exists(model_path):
        print(f"❌ Model directory not found: {model_path}")
        print("Run 'python download_model.py' to download the model")
        return False
    
    required_files = [
        "llm_config.json",
        "vit_config.json", 
        "ae.safetensors",
        "ema.safetensors"
    ]
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if os.path.exists(file_path):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} (missing)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files")
        print("Run 'python download_model.py' to download the model")
        return False
    
    print("✅ All required model files found")
    return True

def test_bagel_imports():
    """Test BAGEL-specific imports"""
    print("\n🥯 Testing BAGEL imports...")
    
    try:
        from data.data_utils import add_special_tokens, pil_img2rgb
        print("✅ Data utils")
    except ImportError as e:
        print(f"❌ Data utils import failed: {e}")
        return False
    
    try:
        from modeling.bagel import BagelConfig, Bagel
        print("✅ BAGEL model")
    except ImportError as e:
        print(f"❌ BAGEL model import failed: {e}")
        return False
    
    try:
        from inferencer import InterleaveInferencer
        print("✅ Inferencer")
    except ImportError as e:
        print(f"❌ Inferencer import failed: {e}")
        return False
    
    return True

def main():
    """Main test function"""
    print("🧪 BAGEL Setup Test")
    print("=" * 40)
    
    # Test basic imports
    if not test_imports():
        print("\n❌ Basic package imports failed")
        sys.exit(1)
    
    # Test BAGEL-specific imports
    if not test_bagel_imports():
        print("\n❌ BAGEL imports failed")
        print("Make sure you're in the correct directory with all BAGEL files")
        sys.exit(1)
    
    # Test model files
    model_ready = test_model_files()
    
    print("\n" + "=" * 40)
    if model_ready:
        print("🎉 All tests passed! BAGEL is ready to use.")
        print("\nTo start the web interface, run:")
        print("python app.py")
    else:
        print("⚠️  Setup is mostly complete, but model files are missing.")
        print("\nTo download the model, run:")
        print("python download_model.py")
        print("\nThen start the web interface with:")
        print("python app.py")

if __name__ == "__main__":
    main()
